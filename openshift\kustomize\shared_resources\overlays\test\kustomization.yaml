---
apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization
namespace: 9b301c-test

secretGenerator:
  - name: ches
    type: stringData
    env: ches.env
  - name: keycloak
    envs:
      - keycloak.env
  - name: database
    type: stringData
    env: database.env

resources:
  - ../../base

generatorOptions:
  disableNameSuffixHash: true

patches:
  - target:
      kind: ConfigMap
      name: ches
    patch: |-
      - op: replace
        path: /data/CHES_FROM
        value: Media Monitoring Insights <<EMAIL>>
  - target:
      kind: ConfigMap
      name: keycloak
    patch: |-
      - op: replace
        path: /data/KEYCLOAK_AUTHORITY
        value: https://test.loginproxy.gov.bc.ca/auth/realms/standard
      - op: replace
        path: /data/CSS_ENVIRONMENT
        value: test
      - op: replace
        path: /data/CSS_AUTHORITY
        value: https://loginproxy.gov.bc.ca
  - target:
      kind: ConfigMap
      name: services
    patch: |-
      - op: replace
        path: /data/KEYCLOAK_AUTHORITY
        value: https://test.loginproxy.gov.bc.ca/auth
  - target:
      kind: ConfigMap
      name: ches
    patch: |-
      - op: replace
        path: /data/CHES_AUTH_URL
        value: https://test.loginproxy.gov.bc.ca/auth/realms/comsvcauth/protocol/openid-connect/token
  - target:
      kind: ConfigMap
      name: ches
    patch: |-
      - op: replace
        path: /data/CHES_HOST_URI
        value: https://ches-test.api.gov.bc.ca/api/v1
  - target:
      kind: ConfigMap
      name: reporting-shared
    patch: |-
      - op: replace
        path: /data/REPORTING_SUBSCRIBER_URL
        value: https://test.mmi.gov.bc.ca/
  - target:
      kind: ConfigMap
      name: reporting-shared
    patch: |-
      - op: replace
        path: /data/REPORTING_VIEW_CONTENT_URL
        value: https://test.mmi.gov.bc.ca/view/
  - target:
      kind: ConfigMap
      name: reporting-shared
    patch: |-
      - op: replace
        path: /data/REPORTING_REQUEST_TRANSCRIPT_URL
        value: https://test.mmi.gov.bc.ca/transcribe/
  - target:
      kind: ConfigMap
      name: reporting-shared
    patch: |-
      - op: replace
        path: /data/REPORTING_ADD_TO_REPORT_URL
        value: https://test.mmi.gov.bc.ca
  - target:
      kind: PersistentVolumeClaim
      name: api-storage
    patch: |-
      - op: replace
        path: /spec/resources/requests/storage
        value: 37Gi
  - target:
      kind: PersistentVolumeClaim
      name: ingest-storage
    patch: |-
      - op: replace
        path: /spec/resources/requests/storage
        value: 35Gi
