{"version": 3, "targets": {"net8.0": {}}, "libraries": {}, "projectFileDependencyGroups": {"net8.0": ["LinqKit >= 1.3.7", "Microsoft.Extensions.Logging.Debug >= 8.0.1", "Oracle.EntityFrameworkCore >= 8.23.60", "System.CommandLine.DragonFruit >= 0.3.0-alpha.20070.2", "TNO.Services >= 1.0.0"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}, "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Documents\\GitHub\\tno\\services\\net\\contentmigration\\TNO.Services.ContentMigration.csproj", "projectName": "TNO.Services.ContentMigration", "projectPath": "C:\\Users\\<USER>\\Documents\\GitHub\\tno\\services\\net\\contentmigration\\TNO.Services.ContentMigration.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Documents\\GitHub\\tno\\services\\net\\contentmigration\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\Documents\\GitHub\\tno\\services\\net\\contentmigration\\NuGet.Config", "C:\\Users\\<USER>\\Documents\\GitHub\\tno\\NuGet.Config", "C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\root\\.nuget\\packages": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"LinqKit": {"target": "Package", "version": "[1.3.7, )"}, "Microsoft.Extensions.Logging.Debug": {"target": "Package", "version": "[8.0.1, )"}, "Oracle.EntityFrameworkCore": {"target": "Package", "version": "[8.23.60, )"}, "System.CommandLine.DragonFruit": {"target": "Package", "version": "[0.3.0-alpha.20070.2, )"}, "TNO.Services": {"target": "Package", "version": "[1.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.302/PortableRuntimeIdentifierGraph.json"}}}, "logs": [{"code": "NU1301", "level": "Error", "message": "The local source 'C:\\root\\.nuget\\packages' doesn't exist.", "libraryId": "TNO.Services"}, {"code": "NU1301", "level": "Error", "message": "The local source 'C:\\root\\.nuget\\packages' doesn't exist.", "libraryId": "Oracle.EntityFrameworkCore"}, {"code": "NU1301", "level": "Error", "message": "The local source 'C:\\root\\.nuget\\packages' doesn't exist.", "libraryId": "Microsoft.Extensions.Logging.Debug"}]}