DO $$
BEGIN

INSERT INTO public."organization"("created_by", "updated_by", "name", "is_enabled") VALUES ('admin', 'admin','AF',true)
,('admin', 'admin','AG',true)
,('admin', 'admin','AudGen',true)
,('admin', 'admin','BC Ferries',true)
,('admin', 'admin','BC Housing',true)
,('admin', 'admin','BC Hydro',true)
,('admin', 'admin','BCER',true)
,('admin', 'admin','BCIB',true)
,('admin', 'admin','BCLC',true)
,('admin', 'admin','BCUC',true)
,('admin', 'admin','CFD',true)
,('admin', 'admin','CITZ',true)
,('admin', 'admin','CLBC',true)
,('admin', 'admin','COI',true)
,('admin', 'admin','DBC',true)
,('admin', 'admin','EAAT',true)
,('admin', 'admin','EAO',true)
,('admin', 'admin','EBC',true)
,('admin', 'admin','ECC',true)
,('admin', 'admin','ECS',true)
,('admin', 'admin','EMCR',true)
,('admin', 'admin','ENV',true)
,('admin', 'admin','FHA',true)
,('admin', 'admin','FIN',true)
,('admin', 'admin','FOR',true)
,('admin', 'admin','GCPE',true)
,('admin', 'admin','GPEB',true)
,('admin', 'admin','HLTH',true)
,('admin', 'admin','HMA',true)
,('admin', 'admin','ICBC',true)
,('admin', 'admin','IGRS',true)
,('admin', 'admin','IHA',true)
,('admin', 'admin','IIO',true)
,('admin', 'admin','INF',true)
,('admin', 'admin','INFBC',true)
,('admin', 'admin','IPC',true)
,('admin', 'admin','IRR',true)
,('admin', 'admin','JEDI',true)
,('admin', 'admin','LBR',true)
,('admin', 'admin','LCLB',true)
,('admin', 'admin','LDB',true)
,('admin', 'admin','LEDGE',true)
,('admin', 'admin','MC',true)
,('admin', 'admin','MCM',true)
,('admin', 'admin','NDP',true)
,('admin', 'admin','NHA',true)
,('admin', 'admin','OoO',true)
,('admin', 'admin','OPCC',true)
,('admin', 'admin','PAVCO',true)
,('admin', 'admin','PGT',true)
,('admin', 'admin','PHSA',true)
,('admin', 'admin','PREM',true)
,('admin', 'admin','PSA',true)
,('admin', 'admin','PSEC',true)
,('admin', 'admin','PSFS',true)
,('admin', 'admin','PSSG',true)
,('admin', 'admin','RCY',true)
,('admin', 'admin','RUBC',true)
,('admin', 'admin','SDPR',true)
,('admin', 'admin','STBC',true)
,('admin', 'admin','TACS',true)
,('admin', 'admin','TICORP',true)
,('admin', 'admin','TransLink',true)
,('admin', 'admin','TT',true)
,('admin', 'admin','VCHA',true)
,('admin', 'admin','VIHA',true)
,('admin', 'admin','WLRS',true);

END $$;