export enum StorageKeys {
  Actions = 'actions',
  Topics = 'topics',
  MediaTypes = 'media_types',
  Sources = 'sources',
  Licenses = 'licenses',
  Series = 'series',
  Ministers = 'ministers',
  Tags = 'tags',
  TonePools = 'tone_pools',
  Rules = 'rules',
  IngestTypes = 'ingest_types',
  Roles = 'roles',
  Contributors = 'contributors',
  SourceActions = 'source_actions',
  Metrics = 'metrics',
  Users = 'users',
  DataLocations = 'data_locations',
  Settings = 'settings',
  Holidays = 'holidays',
  SystemMessages = 'system_messages',
  Organizations = 'organizations',
}
