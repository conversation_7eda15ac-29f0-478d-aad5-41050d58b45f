{"Logging": {"LogLevel": {"Default": "Warning", "Microsoft": "Warning", "TNO": "Debug", "TNO.Services.ApiService": "Debug"}}, "Service": {"MaxFailLimit": 5, "DefaultFrom": "Media Monitoring Insights <<EMAIL>>"}, "Reporting": {"SubscriberAppUrl": "https://dev.mmi.gov.bc.ca", "ViewContentUrl": "https://dev.mmi.gov.bc.ca/view/", "RequestTranscriptUrl": "https://dev.mmi.gov.bc.ca/api/subscriber/work/orders/transcribe/"}, "CHES": {"AuthUrl": "https://dev.loginproxy.gov.bc.ca/auth/realms/comsvcauth/protocol/openid-connect/token", "HostUri": "https://ches-dev.api.gov.bc.ca/api/v1", "EmailEnabled": true, "EmailAuthorized": false}, "Kafka": {"Consumer": {"BootstrapServers": "host.docker.internal:40102"}}, "Auth": {"Keycloak": {"Authority": "https://dev.loginproxy.gov.bc.ca/auth", "Audience": "mmi-service-account", "Secret": "{DO NOT STORE SECRET HERE}"}, "OIDC": {"Token": "/realms/mmi/protocol/openid-connect/token"}}}