{"Logging": {"LogLevel": {"Default": "Warning", "Microsoft": "Error", "TNO": "Information"}}, "Service": {"MaxFailLimit": 5, "ApiUrl": "http://api:8080", "DefaultFrom": "Media Monitoring Insights <<EMAIL>>"}, "Reporting": {"SubscriberAppUrl": "https://test.mmi.gov.bc.ca", "ViewContentUrl": "https://test.mmi.gov.bc.ca/view/", "RequestTranscriptUrl": "https://test.mmi.gov.bc.ca/api/subscriber/work/orders/transcribe/"}, "CHES": {"AuthUrl": "https://test.loginproxy.gov.bc.ca/auth/realms/comsvcauth/protocol/openid-connect/token", "HostUri": "https://ches-test.api.gov.bc.ca/api/v1", "EmailEnabled": true, "EmailAuthorized": false}, "Kafka": {"Consumer": {"BootstrapServers": "kafka-broker-0.kafka-headless:9092,kafka-broker-1.kafka-headless:9092,kafka-broker-2.kafka-headless:9092"}}, "Auth": {"Keycloak": {"Authority": "https://test.loginproxy.gov.bc.ca/auth", "Audience": "mmi-service-account", "Secret": "{DO NOT STORE SECRET HERE}"}, "OIDC": {"Token": "/realms/mmi/protocol/openid-connect/token"}}}