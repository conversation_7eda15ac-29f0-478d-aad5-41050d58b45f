{"name": "mmi-subscriber-app", "description": "Media Monitoring Insights Subscriber application", "version": "0.1.0", "license": "Apache-2.0", "homepage": "/", "packageManager": "yarn@3.2.0", "engines": {"npm": ">=8.19.2 <9.0.0", "node": ">=18.11.0 <19.0.0", "yarn": ">=3.2.0 <4.0.0"}, "dependencies": {"@elastic/elasticsearch": "8.13.1", "@emotion/is-prop-valid": "1.2.2", "@fortawesome/fontawesome-svg-core": "6.5.2", "@microsoft/signalr": "8.0.0", "@react-keycloak/web": "3.4.0", "@reduxjs/toolkit": "2.2.5", "@types/prismjs": "^1.26.5", "axios": "1.7.2", "chart.js": "4.4.4", "chartjs-plugin-datalabels": "2.2.0", "formik": "2.4.6", "html-react-parser": "5.1.10", "keycloak-js": "24.0.4", "lodash": "4.17.21", "lodash.throttle": "4.1.1", "moment": "2.30.1", "prismjs": "^1.30.0", "react": "18.3.1", "react-beautiful-dnd": "13.1.1", "react-color": "2.19.3", "react-datepicker": "6.9.0", "react-dom": "18.3.1", "react-draggable": "4.4.6", "react-error-boundary": "4.0.13", "react-icons": "5.2.1", "react-redux": "9.1.2", "react-router-dom": "6.23.1", "react-scripts": "5.0.1", "react-select": "5.8.0", "react-simple-code-editor": "^0.14.1", "react-table": "7.8.0", "react-toastify": "10.0.5", "react-tooltip": "5.26.4", "redux-logger": "3.0.6", "sheetjs": "file:packages/xlsx-0.20.1.tgz", "styled-components": "6.1.11", "stylis": "4.3.2", "tno-core": "1.0.18"}, "devDependencies": {"@testing-library/jest-dom": "6.6.3", "@testing-library/react": "15.0.7", "@testing-library/react-hooks": "8.0.1", "@testing-library/user-event": "14.5.2", "@types/customize-cra": "1.0.8", "@types/jest": "29.5.12", "@types/js-beautify": "1.14.3", "@types/lodash.throttle": "4.1.9", "@types/node": "20.12.12", "@types/pretty": "2.0.3", "@types/react": "18.3.3", "@types/react-beautiful-dnd": "13.1.8", "@types/react-color": "3.0.12", "@types/react-datepicker": "6.2.0", "@types/react-dom": "18.3.0", "@types/react-redux": "7.1.33", "@types/react-router-dom": "5.3.3", "@types/react-table": "7.7.20", "@types/react-text-mask": "5.4.14", "@types/redux-logger": "3.0.13", "@types/styled-components": "5.1.34", "@types/testing-library__jest-dom": "6.0.0", "@typescript-eslint/eslint-plugin": "5.55.0", "@typescript-eslint/parser": "5.55.0", "@vitejs/plugin-react": "4.3.4", "@vitest/coverage-v8": "3.0.8", "@vitest/ui": "3.0.7", "babel-plugin-prismjs": "^2.1.0", "compression-webpack-plugin": "11.1.0", "eslint": "8.36.0", "eslint-config-prettier": "8.7.0", "eslint-config-react-app": "7.0.1", "eslint-plugin-flowtype": "8.0.3", "eslint-plugin-prettier": "4.2.1", "eslint-plugin-react-hooks": "4.6.2", "eslint-plugin-simple-import-sort": "10.0.0", "jsdom": "26.0.0", "prettier": "2.8.4", "pretty-quick": "4.0.0", "sass": "1.77.2", "sass-extract": "3.0.0", "sass-extract-js": "0.4.0", "sass-extract-loader": "1.1.0", "typescript": "4.9.5", "vitest": "3.0.7"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "eject": "react-scripts eject", "pretty-quick": "pretty-quick", "lint": "eslint src/ --ext .jsx,.js,.ts,.tsx --max-warnings 0", "lint:fix": "npm run lint -- --fix", "format": "prettier --write \"./src/**/*.{js,jsx,ts,tsx,json,css,scss}\"", "check": "prettier --check \"./src/**/*.{js,jsx,ts,tsx,css,scss}\"", "test": "vitest run", "test:watch": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest run --coverage"}, "eslintConfig": {"parser": "@typescript-eslint/parser", "extends": ["react-app", "plugin:prettier/recommended"], "plugins": ["simple-import-sort", "react"], "rules": {"simple-import-sort/imports": "error", "simple-import-sort/exports": "error", "react/jsx-key": "error"}}, "jest": {"transformIgnorePatterns": ["/node_modules/(?!tno-core|axios/)"], "moduleNameMapper": {"\\.(css|less)$": "<rootDir>/__mocks__/styleMock.ts"}}, "babel": {"presets": ["@babel/react", "@babel/env"], "plugins": ["@babel/proposal-class-properties", "@babel/plugin-transform-runtime"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}