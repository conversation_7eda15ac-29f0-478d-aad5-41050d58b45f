{"BaseUrl": "/", "Logging": {"Console": {"DisableColors": true}, "LogLevel": {"Default": "Warning", "Microsoft": "Error", "TNO": "Information", "TNO.Services.ApiService": "Error"}}, "AllowedHosts": "*", "Service": {"MaxFailLimit": 5, "ApiUrl": "http://api:8080", "TimeZone": "Pacific Standard Time", "Topics": "reporting", "SendEmailOnFailure": true, "DefaultFrom": "Media Monitoring Insights <<EMAIL>>"}, "Reporting": {"SubscriberAppUrl": "https://mmi.gov.bc.ca", "ViewContentUrl": "https://mmi.gov.bc.ca/view/", "RequestTranscriptUrl": "https://mmi.gov.bc.ca/api/subscriber/work/orders/transcribe/"}, "Charts": {"Url": "http://charts-api:8080", "Base64Path": "/base64", "ImagePath": "/graph"}, "CHES": {"AuthUrl": "https://loginproxy.gov.bc.ca/auth/realms/comsvcauth/protocol/openid-connect/token", "HostUri": "https://ches.api.gov.bc.ca/api/v1", "EmailEnabled": true, "EmailAuthorized": false}, "Auth": {"Keycloak": {"Authority": "https://loginproxy.gov.bc.ca/auth", "Audience": "mmi-service-account", "Secret": "{DO NOT STORE SECRET HERE}"}, "OIDC": {"Token": "/realms/mmi/protocol/openid-connect/token"}}, "Serialization": {"Json": {"PropertyNamingPolicy": "CamelCase", "PropertyNameCaseInsensitive": true, "DefaultIgnoreCondition": "WhenWritingNull", "WriteIndented": true}}, "Kafka": {"Consumer": {"GroupId": "Reporting", "BootstrapServers": "kafka-broker-0.kafka-headless:9092,kafka-broker-1.kafka-headless:9092,kafka-broker-2.kafka-headless:9092", "AutoOffsetReset": "Latest", "MaxInFlight": 5, "EnableAutoCommit": false, "MaxThreads": 1}}}