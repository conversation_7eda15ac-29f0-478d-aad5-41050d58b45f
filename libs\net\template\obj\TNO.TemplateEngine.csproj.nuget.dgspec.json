{"format": 1, "restore": {"C:\\Users\\<USER>\\Documents\\GitHub\\tno\\libs\\net\\template\\TNO.TemplateEngine.csproj": {}}, "projects": {"C:\\Users\\<USER>\\Documents\\GitHub\\tno\\libs\\net\\core\\TNO.Core.csproj": {"version": "2.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Documents\\GitHub\\tno\\libs\\net\\core\\TNO.Core.csproj", "projectName": "TNO.Core", "projectPath": "C:\\Users\\<USER>\\Documents\\GitHub\\tno\\libs\\net\\core\\TNO.Core.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Documents\\GitHub\\tno\\libs\\net\\core\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\Documents\\GitHub\\tno\\NuGet.Config", "C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Users\\<USER>\\Documents\\GitHub\\tno\\libs\\net\\packages": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"AWSSDK.Extensions.NETCore.Setup": {"target": "Package", "version": "[3.7.301, )"}, "AWSSDK.S3": {"target": "Package", "version": "[3.7.402.5, )"}, "Microsoft.AspNetCore.Http": {"target": "Package", "version": "[2.2.2, )"}, "Microsoft.EntityFrameworkCore": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.EntityFrameworkCore.Design": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[9.0.0, )"}, "Microsoft.EntityFrameworkCore.Tools": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[9.0.0, )"}, "Microsoft.Extensions.Configuration.Abstractions": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.Extensions.Configuration.Binder": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.Extensions.Http": {"target": "Package", "version": "[9.0.0, )"}, "System.IdentityModel.Tokens.Jwt": {"target": "Package", "version": "[8.2.1, )"}, "System.Text.Encodings.Web": {"target": "Package", "version": "[9.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Runtime.linux-arm", "version": "[9.0.7, 9.0.7]"}, {"name": "Microsoft.AspNetCore.App.Runtime.linux-arm64", "version": "[9.0.7, 9.0.7]"}, {"name": "Microsoft.AspNetCore.App.Runtime.linux-x64", "version": "[9.0.7, 9.0.7]"}, {"name": "Microsoft.AspNetCore.App.Runtime.win-x64", "version": "[9.0.7, 9.0.7]"}, {"name": "Microsoft.NETCore.App.Host.linux-arm", "version": "[9.0.7, 9.0.7]"}, {"name": "Microsoft.NETCore.App.Host.linux-arm64", "version": "[9.0.7, 9.0.7]"}, {"name": "Microsoft.NETCore.App.Host.linux-x64", "version": "[9.0.7, 9.0.7]"}, {"name": "Microsoft.NETCore.App.Runtime.linux-arm", "version": "[9.0.7, 9.0.7]"}, {"name": "Microsoft.NETCore.App.Runtime.linux-arm64", "version": "[9.0.7, 9.0.7]"}, {"name": "Microsoft.NETCore.App.Runtime.linux-x64", "version": "[9.0.7, 9.0.7]"}, {"name": "Microsoft.NETCore.App.Runtime.win-x64", "version": "[9.0.7, 9.0.7]"}, {"name": "Microsoft.WindowsDesktop.App.Runtime.win-x64", "version": "[9.0.7, 9.0.7]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.302/PortableRuntimeIdentifierGraph.json"}}, "runtimes": {"linux-arm": {"#import": []}, "linux-arm64": {"#import": []}, "linux-x64": {"#import": []}, "win-x64": {"#import": []}}}, "C:\\Users\\<USER>\\Documents\\GitHub\\tno\\libs\\net\\elastic\\TNO.Elastic.csproj": {"version": "2.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Documents\\GitHub\\tno\\libs\\net\\elastic\\TNO.Elastic.csproj", "projectName": "TNO.Elastic", "projectPath": "C:\\Users\\<USER>\\Documents\\GitHub\\tno\\libs\\net\\elastic\\TNO.Elastic.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Documents\\GitHub\\tno\\libs\\net\\elastic\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\Documents\\GitHub\\tno\\NuGet.Config", "C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Users\\<USER>\\Documents\\GitHub\\tno\\libs\\net\\packages": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"C:\\Users\\<USER>\\Documents\\GitHub\\tno\\libs\\net\\core\\TNO.Core.csproj": {"projectPath": "C:\\Users\\<USER>\\Documents\\GitHub\\tno\\libs\\net\\core\\TNO.Core.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"Elasticsearch.Net": {"target": "Package", "version": "[7.17.5, )"}, "Microsoft.Extensions.Hosting": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.Extensions.Options.DataAnnotations": {"target": "Package", "version": "[9.0.0, )"}, "NEST": {"target": "Package", "version": "[7.17.5, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Runtime.linux-arm", "version": "[9.0.7, 9.0.7]"}, {"name": "Microsoft.AspNetCore.App.Runtime.linux-arm64", "version": "[9.0.7, 9.0.7]"}, {"name": "Microsoft.AspNetCore.App.Runtime.linux-x64", "version": "[9.0.7, 9.0.7]"}, {"name": "Microsoft.AspNetCore.App.Runtime.win-x64", "version": "[9.0.7, 9.0.7]"}, {"name": "Microsoft.NETCore.App.Host.linux-arm", "version": "[9.0.7, 9.0.7]"}, {"name": "Microsoft.NETCore.App.Host.linux-arm64", "version": "[9.0.7, 9.0.7]"}, {"name": "Microsoft.NETCore.App.Host.linux-x64", "version": "[9.0.7, 9.0.7]"}, {"name": "Microsoft.NETCore.App.Runtime.linux-arm", "version": "[9.0.7, 9.0.7]"}, {"name": "Microsoft.NETCore.App.Runtime.linux-arm64", "version": "[9.0.7, 9.0.7]"}, {"name": "Microsoft.NETCore.App.Runtime.linux-x64", "version": "[9.0.7, 9.0.7]"}, {"name": "Microsoft.NETCore.App.Runtime.win-x64", "version": "[9.0.7, 9.0.7]"}, {"name": "Microsoft.WindowsDesktop.App.Runtime.win-x64", "version": "[9.0.7, 9.0.7]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.302/PortableRuntimeIdentifierGraph.json"}}, "runtimes": {"linux-arm": {"#import": []}, "linux-arm64": {"#import": []}, "linux-x64": {"#import": []}, "win-x64": {"#import": []}}}, "C:\\Users\\<USER>\\Documents\\GitHub\\tno\\libs\\net\\entities\\TNO.Entities.csproj": {"version": "2.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Documents\\GitHub\\tno\\libs\\net\\entities\\TNO.Entities.csproj", "projectName": "TNO.Entities", "projectPath": "C:\\Users\\<USER>\\Documents\\GitHub\\tno\\libs\\net\\entities\\TNO.Entities.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Documents\\GitHub\\tno\\libs\\net\\entities\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\Documents\\GitHub\\tno\\NuGet.Config", "C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Users\\<USER>\\Documents\\GitHub\\tno\\libs\\net\\packages": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"C:\\Users\\<USER>\\Documents\\GitHub\\tno\\libs\\net\\core\\TNO.Core.csproj": {"projectPath": "C:\\Users\\<USER>\\Documents\\GitHub\\tno\\libs\\net\\core\\TNO.Core.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"Microsoft.EntityFrameworkCore": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.EntityFrameworkCore.Design": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[9.0.0, )"}, "Microsoft.EntityFrameworkCore.Relational": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.EntityFrameworkCore.Tools": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[9.0.0, )"}, "Npgsql.EntityFrameworkCore.PostgreSQL": {"target": "Package", "version": "[9.0.1, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Runtime.linux-arm", "version": "[9.0.7, 9.0.7]"}, {"name": "Microsoft.AspNetCore.App.Runtime.linux-arm64", "version": "[9.0.7, 9.0.7]"}, {"name": "Microsoft.AspNetCore.App.Runtime.linux-x64", "version": "[9.0.7, 9.0.7]"}, {"name": "Microsoft.AspNetCore.App.Runtime.win-x64", "version": "[9.0.7, 9.0.7]"}, {"name": "Microsoft.NETCore.App.Host.linux-arm", "version": "[9.0.7, 9.0.7]"}, {"name": "Microsoft.NETCore.App.Host.linux-arm64", "version": "[9.0.7, 9.0.7]"}, {"name": "Microsoft.NETCore.App.Host.linux-x64", "version": "[9.0.7, 9.0.7]"}, {"name": "Microsoft.NETCore.App.Runtime.linux-arm", "version": "[9.0.7, 9.0.7]"}, {"name": "Microsoft.NETCore.App.Runtime.linux-arm64", "version": "[9.0.7, 9.0.7]"}, {"name": "Microsoft.NETCore.App.Runtime.linux-x64", "version": "[9.0.7, 9.0.7]"}, {"name": "Microsoft.NETCore.App.Runtime.win-x64", "version": "[9.0.7, 9.0.7]"}, {"name": "Microsoft.WindowsDesktop.App.Runtime.win-x64", "version": "[9.0.7, 9.0.7]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.302/PortableRuntimeIdentifierGraph.json"}}, "runtimes": {"linux-arm": {"#import": []}, "linux-arm64": {"#import": []}, "linux-x64": {"#import": []}, "win-x64": {"#import": []}}}, "C:\\Users\\<USER>\\Documents\\GitHub\\tno\\libs\\net\\models\\TNO.Models.csproj": {"version": "2.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Documents\\GitHub\\tno\\libs\\net\\models\\TNO.Models.csproj", "projectName": "TNO.Models", "projectPath": "C:\\Users\\<USER>\\Documents\\GitHub\\tno\\libs\\net\\models\\TNO.Models.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Documents\\GitHub\\tno\\libs\\net\\models\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\Documents\\GitHub\\tno\\NuGet.Config", "C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Users\\<USER>\\Documents\\GitHub\\tno\\libs\\net\\packages": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"C:\\Users\\<USER>\\Documents\\GitHub\\tno\\libs\\net\\entities\\TNO.Entities.csproj": {"projectPath": "C:\\Users\\<USER>\\Documents\\GitHub\\tno\\libs\\net\\entities\\TNO.Entities.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"FTTLib.dll": {"target": "Package", "version": "[1.1.7, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Runtime.linux-arm", "version": "[9.0.7, 9.0.7]"}, {"name": "Microsoft.AspNetCore.App.Runtime.linux-arm64", "version": "[9.0.7, 9.0.7]"}, {"name": "Microsoft.AspNetCore.App.Runtime.linux-x64", "version": "[9.0.7, 9.0.7]"}, {"name": "Microsoft.AspNetCore.App.Runtime.win-x64", "version": "[9.0.7, 9.0.7]"}, {"name": "Microsoft.NETCore.App.Host.linux-arm", "version": "[9.0.7, 9.0.7]"}, {"name": "Microsoft.NETCore.App.Host.linux-arm64", "version": "[9.0.7, 9.0.7]"}, {"name": "Microsoft.NETCore.App.Host.linux-x64", "version": "[9.0.7, 9.0.7]"}, {"name": "Microsoft.NETCore.App.Runtime.linux-arm", "version": "[9.0.7, 9.0.7]"}, {"name": "Microsoft.NETCore.App.Runtime.linux-arm64", "version": "[9.0.7, 9.0.7]"}, {"name": "Microsoft.NETCore.App.Runtime.linux-x64", "version": "[9.0.7, 9.0.7]"}, {"name": "Microsoft.NETCore.App.Runtime.win-x64", "version": "[9.0.7, 9.0.7]"}, {"name": "Microsoft.WindowsDesktop.App.Runtime.win-x64", "version": "[9.0.7, 9.0.7]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.302/PortableRuntimeIdentifierGraph.json"}}, "runtimes": {"linux-arm": {"#import": []}, "linux-arm64": {"#import": []}, "linux-x64": {"#import": []}, "win-x64": {"#import": []}}}, "C:\\Users\\<USER>\\Documents\\GitHub\\tno\\libs\\net\\template\\TNO.TemplateEngine.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Documents\\GitHub\\tno\\libs\\net\\template\\TNO.TemplateEngine.csproj", "projectName": "TNO.TemplateEngine", "projectPath": "C:\\Users\\<USER>\\Documents\\GitHub\\tno\\libs\\net\\template\\TNO.TemplateEngine.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Documents\\GitHub\\tno\\libs\\net\\template\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\Documents\\GitHub\\tno\\NuGet.Config", "C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Users\\<USER>\\Documents\\GitHub\\tno\\libs\\net\\packages": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"C:\\Users\\<USER>\\Documents\\GitHub\\tno\\libs\\net\\core\\TNO.Core.csproj": {"projectPath": "C:\\Users\\<USER>\\Documents\\GitHub\\tno\\libs\\net\\core\\TNO.Core.csproj"}, "C:\\Users\\<USER>\\Documents\\GitHub\\tno\\libs\\net\\elastic\\TNO.Elastic.csproj": {"projectPath": "C:\\Users\\<USER>\\Documents\\GitHub\\tno\\libs\\net\\elastic\\TNO.Elastic.csproj"}, "C:\\Users\\<USER>\\Documents\\GitHub\\tno\\libs\\net\\entities\\TNO.Entities.csproj": {"projectPath": "C:\\Users\\<USER>\\Documents\\GitHub\\tno\\libs\\net\\entities\\TNO.Entities.csproj"}, "C:\\Users\\<USER>\\Documents\\GitHub\\tno\\libs\\net\\models\\TNO.Models.csproj": {"projectPath": "C:\\Users\\<USER>\\Documents\\GitHub\\tno\\libs\\net\\models\\TNO.Models.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"Microsoft.Extensions.DependencyInjection": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.Extensions.Options.ConfigurationExtensions": {"target": "Package", "version": "[9.0.0, )"}, "RazorEngineCore": {"target": "Package", "version": "[2024.4.1, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.302/PortableRuntimeIdentifierGraph.json"}}}}}