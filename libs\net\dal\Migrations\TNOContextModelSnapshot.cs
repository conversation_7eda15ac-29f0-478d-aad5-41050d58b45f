﻿// <auto-generated />
using System;
using System.Collections.Generic;
using System.Text.Json;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;
using TNO.DAL;
using TNO.Entities.Models;

#nullable disable

namespace TNO.DAL.Migrations
{
    [DbContext(typeof(TNOContext))]
    partial class TNOContextModelSnapshot : ModelSnapshot
    {
        protected override void BuildModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "9.0.0")
                .HasAnnotation("Relational:MaxIdentifierLength", 63);

            NpgsqlModelBuilderExtensions.UseIdentityByDefaultColumns(modelBuilder);

            modelBuilder.Entity("TNO.Entities.AVOverviewInstance", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<long>("Id"));

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("character varying(250)")
                        .HasColumnName("created_by");

                    b.Property<DateTime>("CreatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_on")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<bool>("IsPublished")
                        .HasColumnType("boolean");

                    b.Property<DateTime>("PublishedOn")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("published_on");

                    b.Property<JsonDocument>("Response")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("jsonb")
                        .HasColumnName("response")
                        .HasDefaultValueSql("'{}'::jsonb");

                    b.Property<int>("TemplateType")
                        .HasColumnType("integer")
                        .HasColumnName("template_type");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("character varying(250)")
                        .HasColumnName("updated_by");

                    b.Property<DateTime>("UpdatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_on")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<long>("Version")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasColumnName("version")
                        .HasDefaultValueSql("0");

                    b.HasKey("Id");

                    b.HasIndex("PublishedOn")
                        .IsUnique();

                    b.HasIndex("TemplateType", "PublishedOn");

                    b.ToTable("av_overview_instance");
                });

            modelBuilder.Entity("TNO.Entities.AVOverviewSection", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("Anchors")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("anchors");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("character varying(250)")
                        .HasColumnName("created_by");

                    b.Property<DateTime>("CreatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_on")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<long>("InstanceId")
                        .HasColumnType("bigint")
                        .HasColumnName("av_overview_instance_id");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("name");

                    b.Property<string>("OtherSource")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("other_source");

                    b.Property<int?>("SeriesId")
                        .HasColumnType("integer")
                        .HasColumnName("series_id");

                    b.Property<int>("SortOrder")
                        .HasColumnType("integer")
                        .HasColumnName("sort_order");

                    b.Property<int?>("SourceId")
                        .HasColumnType("integer")
                        .HasColumnName("source_id");

                    b.Property<string>("StartTime")
                        .IsRequired()
                        .HasMaxLength(8)
                        .HasColumnType("character varying(8)")
                        .HasColumnName("start_time");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("character varying(250)")
                        .HasColumnName("updated_by");

                    b.Property<DateTime>("UpdatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_on")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<long>("Version")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasColumnName("version")
                        .HasDefaultValueSql("0");

                    b.HasKey("Id");

                    b.HasIndex("InstanceId");

                    b.HasIndex("SeriesId");

                    b.HasIndex("SourceId");

                    b.ToTable("av_overview_section");
                });

            modelBuilder.Entity("TNO.Entities.AVOverviewSectionItem", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<long?>("ContentId")
                        .HasColumnType("bigint")
                        .HasColumnName("content_id");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("character varying(250)")
                        .HasColumnName("created_by");

                    b.Property<DateTime>("CreatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_on")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<int>("ItemType")
                        .HasColumnType("integer")
                        .HasColumnName("item_type");

                    b.Property<int>("SectionId")
                        .HasColumnType("integer")
                        .HasColumnName("av_overview_section_id");

                    b.Property<int>("SortOrder")
                        .HasColumnType("integer")
                        .HasColumnName("sort_order");

                    b.Property<string>("Summary")
                        .IsRequired()
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)")
                        .HasColumnName("summary");

                    b.Property<string>("Time")
                        .IsRequired()
                        .HasMaxLength(8)
                        .HasColumnType("character varying(8)")
                        .HasColumnName("time");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("character varying(250)")
                        .HasColumnName("updated_by");

                    b.Property<DateTime>("UpdatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_on")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<long>("Version")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasColumnName("version")
                        .HasDefaultValueSql("0");

                    b.HasKey("Id");

                    b.HasIndex("ContentId");

                    b.HasIndex("SectionId");

                    b.ToTable("av_overview_section_item");
                });

            modelBuilder.Entity("TNO.Entities.AVOverviewTemplate", b =>
                {
                    b.Property<int>("TemplateType")
                        .HasColumnType("integer")
                        .HasColumnName("template_type");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("character varying(250)")
                        .HasColumnName("created_by");

                    b.Property<DateTime>("CreatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_on")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<int>("TemplateId")
                        .HasColumnType("integer")
                        .HasColumnName("report_template_id");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("character varying(250)")
                        .HasColumnName("updated_by");

                    b.Property<DateTime>("UpdatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_on")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<long>("Version")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasColumnName("version")
                        .HasDefaultValueSql("0");

                    b.HasKey("TemplateType");

                    b.HasIndex("TemplateId");

                    b.ToTable("av_overview_template");
                });

            modelBuilder.Entity("TNO.Entities.AVOverviewTemplateSection", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("Anchors")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("anchors");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("character varying(250)")
                        .HasColumnName("created_by");

                    b.Property<DateTime>("CreatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_on")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("name");

                    b.Property<string>("OtherSource")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("other_source");

                    b.Property<int?>("SeriesId")
                        .HasColumnType("integer")
                        .HasColumnName("series_id");

                    b.Property<int>("SortOrder")
                        .HasColumnType("integer")
                        .HasColumnName("sort_order");

                    b.Property<int?>("SourceId")
                        .HasColumnType("integer")
                        .HasColumnName("source_id");

                    b.Property<string>("StartTime")
                        .IsRequired()
                        .HasMaxLength(8)
                        .HasColumnType("character varying(8)")
                        .HasColumnName("start_time");

                    b.Property<int>("TemplateType")
                        .HasColumnType("integer")
                        .HasColumnName("av_overview_template_id");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("character varying(250)")
                        .HasColumnName("updated_by");

                    b.Property<DateTime>("UpdatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_on")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<long>("Version")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasColumnName("version")
                        .HasDefaultValueSql("0");

                    b.HasKey("Id");

                    b.HasIndex("SeriesId");

                    b.HasIndex("SourceId");

                    b.HasIndex("TemplateType");

                    b.ToTable("av_overview_template_section");
                });

            modelBuilder.Entity("TNO.Entities.AVOverviewTemplateSectionItem", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("character varying(250)")
                        .HasColumnName("created_by");

                    b.Property<DateTime>("CreatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_on")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<int>("ItemType")
                        .HasColumnType("integer")
                        .HasColumnName("item_type");

                    b.Property<int>("SectionId")
                        .HasColumnType("integer")
                        .HasColumnName("av_overview_template_section_id");

                    b.Property<int>("SortOrder")
                        .HasColumnType("integer")
                        .HasColumnName("sort_order");

                    b.Property<string>("Summary")
                        .IsRequired()
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)")
                        .HasColumnName("summary");

                    b.Property<string>("Time")
                        .IsRequired()
                        .HasMaxLength(8)
                        .HasColumnType("character varying(8)")
                        .HasColumnName("time");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("character varying(250)")
                        .HasColumnName("updated_by");

                    b.Property<DateTime>("UpdatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_on")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<long>("Version")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasColumnName("version")
                        .HasDefaultValueSql("0");

                    b.HasKey("Id");

                    b.HasIndex("SectionId");

                    b.ToTable("av_overview_template_section_item");
                });

            modelBuilder.Entity("TNO.Entities.Action", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("character varying(250)")
                        .HasColumnName("created_by");

                    b.Property<DateTime>("CreatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_on")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<string>("DefaultValue")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("default_value")
                        .HasDefaultValueSql("''");

                    b.Property<string>("Description")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)")
                        .HasColumnName("description")
                        .HasDefaultValueSql("''");

                    b.Property<bool>("IsEnabled")
                        .HasColumnType("boolean")
                        .HasColumnName("is_enabled");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("name");

                    b.Property<int>("SortOrder")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(0)
                        .HasColumnName("sort_order");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("character varying(250)")
                        .HasColumnName("updated_by");

                    b.Property<DateTime>("UpdatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_on")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<string>("ValueLabel")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("value_label")
                        .HasDefaultValueSql("''");

                    b.Property<int>("ValueType")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(0)
                        .HasColumnName("value_type");

                    b.Property<long>("Version")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasColumnName("version")
                        .HasDefaultValueSql("0");

                    b.HasKey("Id");

                    b.HasIndex(new[] { "ValueType", "ValueLabel" }, "IX_action");

                    b.HasIndex(new[] { "IsEnabled", "Name" }, "IX_action_is_enabled");

                    b.HasIndex(new[] { "Name" }, "IX_name")
                        .IsUnique();

                    b.ToTable("action");
                });

            modelBuilder.Entity("TNO.Entities.CBRAReportStaffSummary", b =>
                {
                    b.Property<string>("CbraHours")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("cbra_hours");

                    b.Property<string>("Staff")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("staff");

                    b.ToTable((string)null);

                    b.ToView(null, (string)null);
                });

            modelBuilder.Entity("TNO.Entities.CBRAReportTotalEntries", b =>
                {
                    b.Property<string>("DayOfWeek")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("day_of_week");

                    b.Property<decimal>("TotalCbra")
                        .HasColumnType("numeric")
                        .HasColumnName("total_cbra");

                    b.Property<decimal>("TotalCount")
                        .HasColumnType("numeric")
                        .HasColumnName("total_count");

                    b.ToTable((string)null);

                    b.ToView(null, (string)null);
                });

            modelBuilder.Entity("TNO.Entities.CBRAReportTotalExcerpts", b =>
                {
                    b.Property<string>("Category")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("category");

                    b.Property<int>("Totals")
                        .HasColumnType("integer")
                        .HasColumnName("totals");

                    b.ToTable((string)null);

                    b.ToView(null, (string)null);
                });

            modelBuilder.Entity("TNO.Entities.CBRAReportTotalsByBroadcaster", b =>
                {
                    b.Property<decimal>("PercentageOfTotalRunningTime")
                        .HasColumnType("numeric")
                        .HasColumnName("percentage_of_total_running_time");

                    b.Property<string>("SourceType")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("source_type");

                    b.Property<string>("TotalRunningTime")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("total_running_time");

                    b.ToTable((string)null);

                    b.ToView(null, (string)null);
                });

            modelBuilder.Entity("TNO.Entities.CBRAReportTotalsByProgram", b =>
                {
                    b.Property<string>("MediaType")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("media_type");

                    b.Property<decimal>("PercentageOfTotalRunningTime")
                        .HasColumnType("numeric")
                        .HasColumnName("percentage_of_total_running_time");

                    b.Property<string>("Series")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("series");

                    b.Property<string>("SourceType")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("source_type");

                    b.Property<decimal>("TotalCount")
                        .HasColumnType("numeric")
                        .HasColumnName("total_count");

                    b.Property<string>("TotalRunningTime")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("total_running_time");

                    b.ToTable((string)null);

                    b.ToView(null, (string)null);
                });

            modelBuilder.Entity("TNO.Entities.Cache", b =>
                {
                    b.Property<string>("Key")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("key");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("character varying(250)")
                        .HasColumnName("created_by");

                    b.Property<DateTime>("CreatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_on")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<string>("Description")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)")
                        .HasColumnName("description")
                        .HasDefaultValueSql("''");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("character varying(250)")
                        .HasColumnName("updated_by");

                    b.Property<DateTime>("UpdatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_on")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<string>("Value")
                        .IsRequired()
                        .HasMaxLength(150)
                        .HasColumnType("character varying(150)")
                        .HasColumnName("value");

                    b.Property<long>("Version")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasColumnName("version")
                        .HasDefaultValueSql("0");

                    b.HasKey("Key");

                    b.HasIndex(new[] { "Key", "Value" }, "IX_cache");

                    b.ToTable("cache");
                });

            modelBuilder.Entity("TNO.Entities.ChartTemplate", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("character varying(250)")
                        .HasColumnName("created_by");

                    b.Property<DateTime>("CreatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_on")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<string>("Description")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)")
                        .HasColumnName("description")
                        .HasDefaultValueSql("''");

                    b.Property<bool>("IsEnabled")
                        .HasColumnType("boolean")
                        .HasColumnName("is_enabled");

                    b.Property<bool>("IsPublic")
                        .HasColumnType("boolean")
                        .HasColumnName("is_public");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("name");

                    b.Property<JsonDocument>("Settings")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("jsonb")
                        .HasColumnName("settings")
                        .HasDefaultValueSql("'{}'::jsonb");

                    b.Property<int>("SortOrder")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(0)
                        .HasColumnName("sort_order");

                    b.Property<string>("Template")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("template");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("character varying(250)")
                        .HasColumnName("updated_by");

                    b.Property<DateTime>("UpdatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_on")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<long>("Version")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasColumnName("version")
                        .HasDefaultValueSql("0");

                    b.HasKey("Id");

                    b.HasIndex("Name")
                        .IsUnique();

                    b.HasIndex(new[] { "IsEnabled", "Name" }, "IX_charttemplate_is_enabled");

                    b.ToTable("chart_template");
                });

            modelBuilder.Entity("TNO.Entities.Connection", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<JsonDocument>("Configuration")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("jsonb")
                        .HasColumnName("configuration")
                        .HasDefaultValueSql("'{}'::jsonb");

                    b.Property<int>("ConnectionType")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(0)
                        .HasColumnName("connection_type");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("character varying(250)")
                        .HasColumnName("created_by");

                    b.Property<DateTime>("CreatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_on")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<string>("Description")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)")
                        .HasColumnName("description")
                        .HasDefaultValueSql("''");

                    b.Property<bool>("IsEnabled")
                        .HasColumnType("boolean")
                        .HasColumnName("is_enabled");

                    b.Property<bool>("IsReadOnly")
                        .HasColumnType("boolean")
                        .HasColumnName("is_read_only");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("name");

                    b.Property<int>("SortOrder")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(0)
                        .HasColumnName("sort_order");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("character varying(250)")
                        .HasColumnName("updated_by");

                    b.Property<DateTime>("UpdatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_on")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<long>("Version")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasColumnName("version")
                        .HasDefaultValueSql("0");

                    b.HasKey("Id");

                    b.HasIndex(new[] { "IsEnabled", "Name" }, "IX_connection_is_enabled");

                    b.HasIndex(new[] { "Name" }, "IX_name")
                        .IsUnique()
                        .HasDatabaseName("IX_name1");

                    b.ToTable("connection");
                });

            modelBuilder.Entity("TNO.Entities.Content", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<long>("Id"));

                    b.Property<string>("Body")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("body");

                    b.Property<string>("Byline")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)")
                        .HasColumnName("byline");

                    b.Property<int>("ContentType")
                        .HasColumnType("integer")
                        .HasColumnName("content_type");

                    b.Property<int?>("ContributorId")
                        .HasColumnType("integer")
                        .HasColumnName("contributor_id");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("character varying(250)")
                        .HasColumnName("created_by");

                    b.Property<DateTime>("CreatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_on")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<string>("Edition")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("edition");

                    b.Property<string>("ExternalUid")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)")
                        .HasDefaultValue("")
                        .HasColumnName("external_uid");

                    b.Property<string>("Headline")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)")
                        .HasColumnName("headline");

                    b.Property<int?>("IngestTypeId")
                        .HasColumnType("integer");

                    b.Property<bool>("IsApproved")
                        .HasColumnType("boolean")
                        .HasColumnName("is_approved");

                    b.Property<bool>("IsHidden")
                        .HasColumnType("boolean")
                        .HasColumnName("is_hidden");

                    b.Property<bool>("IsPrivate")
                        .HasColumnType("boolean")
                        .HasColumnName("is_private");

                    b.Property<int>("LicenseId")
                        .HasColumnType("integer")
                        .HasColumnName("license_id");

                    b.Property<int>("MediaTypeId")
                        .HasColumnType("integer")
                        .HasColumnName("media_type_id");

                    b.Property<string>("OtherSource")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("source");

                    b.Property<int?>("OwnerId")
                        .HasColumnType("integer")
                        .HasColumnName("owner_id");

                    b.Property<string>("Page")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)")
                        .HasColumnName("page");

                    b.Property<DateTime?>("PostedOn")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("posted_on");

                    b.Property<DateTime?>("PublishedOn")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("published_on");

                    b.Property<string>("Section")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("section");

                    b.Property<int?>("SeriesId")
                        .HasColumnType("integer")
                        .HasColumnName("series_id");

                    b.Property<int?>("SourceId")
                        .HasColumnType("integer")
                        .HasColumnName("source_id");

                    b.Property<string>("SourceUrl")
                        .IsRequired()
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)")
                        .HasColumnName("source_url");

                    b.Property<int>("Status")
                        .HasColumnType("integer")
                        .HasColumnName("status");

                    b.Property<string>("Summary")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("summary");

                    b.Property<string>("Uid")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)")
                        .HasColumnName("uid");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("character varying(250)")
                        .HasColumnName("updated_by");

                    b.Property<DateTime>("UpdatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_on")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<long>("Version")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasColumnName("version")
                        .HasDefaultValueSql("0");

                    b.Property<Dictionary<int, ContentVersion>>("Versions")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("jsonb")
                        .HasColumnName("versions")
                        .HasDefaultValueSql("'{}'::jsonb");

                    b.HasKey("Id");

                    b.HasIndex("ContributorId");

                    b.HasIndex("IngestTypeId");

                    b.HasIndex("LicenseId");

                    b.HasIndex("MediaTypeId");

                    b.HasIndex("OwnerId");

                    b.HasIndex("SeriesId");

                    b.HasIndex("SourceId");

                    b.HasIndex(new[] { "ContentType", "OtherSource", "Uid", "Page", "Status", "IsHidden" }, "IX_content");

                    b.HasIndex(new[] { "PublishedOn", "CreatedOn" }, "IX_content_dates");

                    b.HasIndex(new[] { "PublishedOn" }, "IX_content_published_on")
                        .IsDescending();

                    b.HasIndex(new[] { "PublishedOn", "Status" }, "IX_content_published_on_status")
                        .IsDescending(true, false);

                    b.HasIndex(new[] { "Headline" }, "IX_headline");

                    b.HasIndex(new[] { "Edition", "Section", "Byline" }, "IX_print_content");

                    b.ToTable("content");
                });

            modelBuilder.Entity("TNO.Entities.ContentAction", b =>
                {
                    b.Property<long>("ContentId")
                        .HasColumnType("bigint")
                        .HasColumnName("content_id");

                    b.Property<int>("ActionId")
                        .HasColumnType("integer")
                        .HasColumnName("action_id");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("character varying(250)")
                        .HasColumnName("created_by");

                    b.Property<DateTime>("CreatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_on")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("character varying(250)")
                        .HasColumnName("updated_by");

                    b.Property<DateTime>("UpdatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_on")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<string>("Value")
                        .IsRequired()
                        .HasMaxLength(150)
                        .HasColumnType("character varying(150)")
                        .HasColumnName("value");

                    b.Property<long>("Version")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasColumnName("version")
                        .HasDefaultValueSql("0");

                    b.HasKey("ContentId", "ActionId");

                    b.HasIndex("ActionId");

                    b.ToTable("content_action");
                });

            modelBuilder.Entity("TNO.Entities.ContentLabel", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<long>("Id"));

                    b.Property<long>("ContentId")
                        .HasColumnType("bigint")
                        .HasColumnName("content_id");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("character varying(250)")
                        .HasColumnName("created_by");

                    b.Property<DateTime>("CreatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_on")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<string>("Key")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("key");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("character varying(250)")
                        .HasColumnName("updated_by");

                    b.Property<DateTime>("UpdatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_on")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<string>("Value")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("character varying(250)")
                        .HasColumnName("value");

                    b.Property<long>("Version")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasColumnName("version")
                        .HasDefaultValueSql("0");

                    b.HasKey("Id");

                    b.HasIndex("ContentId");

                    b.HasIndex(new[] { "Key", "Value" }, "IX_content_label");

                    b.ToTable("content_label");
                });

            modelBuilder.Entity("TNO.Entities.ContentLink", b =>
                {
                    b.Property<long>("ContentId")
                        .HasColumnType("bigint")
                        .HasColumnName("content_id");

                    b.Property<long>("LinkId")
                        .HasColumnType("bigint")
                        .HasColumnName("link_id");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("character varying(250)")
                        .HasColumnName("created_by");

                    b.Property<DateTime>("CreatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_on")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("character varying(250)")
                        .HasColumnName("updated_by");

                    b.Property<DateTime>("UpdatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_on")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<string>("Value")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("value");

                    b.Property<long>("Version")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasColumnName("version")
                        .HasDefaultValueSql("0");

                    b.HasKey("ContentId", "LinkId");

                    b.HasIndex("LinkId");

                    b.ToTable("content_link");
                });

            modelBuilder.Entity("TNO.Entities.ContentLog", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<long>("Id"));

                    b.Property<long>("ContentId")
                        .HasColumnType("bigint")
                        .HasColumnName("content_id");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("character varying(250)")
                        .HasColumnName("created_by");

                    b.Property<DateTime>("CreatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_on")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<string>("Message")
                        .IsRequired()
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)")
                        .HasColumnName("message");

                    b.Property<int>("Status")
                        .HasColumnType("integer")
                        .HasColumnName("status");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("character varying(250)")
                        .HasColumnName("updated_by");

                    b.Property<DateTime>("UpdatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_on")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<long>("Version")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasColumnName("version")
                        .HasDefaultValueSql("0");

                    b.HasKey("Id");

                    b.HasIndex("ContentId");

                    b.ToTable("content_log");
                });

            modelBuilder.Entity("TNO.Entities.ContentReference", b =>
                {
                    b.Property<string>("Source")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("source");

                    b.Property<string>("Uid")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)")
                        .HasColumnName("uid");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("character varying(250)")
                        .HasColumnName("created_by");

                    b.Property<DateTime>("CreatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_on")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<JsonDocument>("Metadata")
                        .HasColumnType("jsonb")
                        .HasColumnName("metadata");

                    b.Property<DateTime?>("PublishedOn")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("published_on");

                    b.Property<DateTime?>("SourceUpdateOn")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("source_updated_on");

                    b.Property<int>("Status")
                        .HasColumnType("integer")
                        .HasColumnName("status");

                    b.Property<string>("Topic")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("topic");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("character varying(250)")
                        .HasColumnName("updated_by");

                    b.Property<DateTime>("UpdatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_on")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<long>("Version")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasColumnName("version")
                        .HasDefaultValueSql("0");

                    b.HasKey("Source", "Uid");

                    b.HasIndex(new[] { "PublishedOn", "Status" }, "IX_content_reference");

                    b.HasIndex(new[] { "Source", "Uid" }, "IX_source_uid");

                    b.ToTable("content_reference");
                });

            modelBuilder.Entity("TNO.Entities.ContentTag", b =>
                {
                    b.Property<long>("ContentId")
                        .HasColumnType("bigint")
                        .HasColumnName("content_id");

                    b.Property<int>("TagId")
                        .HasColumnType("integer")
                        .HasColumnName("tag_id");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("character varying(250)")
                        .HasColumnName("created_by");

                    b.Property<DateTime>("CreatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_on")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("character varying(250)")
                        .HasColumnName("updated_by");

                    b.Property<DateTime>("UpdatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_on")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<long>("Version")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasColumnName("version")
                        .HasDefaultValueSql("0");

                    b.HasKey("ContentId", "TagId");

                    b.HasIndex("TagId");

                    b.ToTable("content_tag");
                });

            modelBuilder.Entity("TNO.Entities.ContentTonePool", b =>
                {
                    b.Property<long>("ContentId")
                        .HasColumnType("bigint")
                        .HasColumnName("content_id");

                    b.Property<int>("TonePoolId")
                        .HasColumnType("integer")
                        .HasColumnName("tone_pool_id");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("character varying(250)")
                        .HasColumnName("created_by");

                    b.Property<DateTime>("CreatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_on")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("character varying(250)")
                        .HasColumnName("updated_by");

                    b.Property<DateTime>("UpdatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_on")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<int>("Value")
                        .HasColumnType("integer")
                        .HasColumnName("value");

                    b.Property<long>("Version")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasColumnName("version")
                        .HasDefaultValueSql("0");

                    b.HasKey("ContentId", "TonePoolId");

                    b.HasIndex("TonePoolId");

                    b.ToTable("content_tone");
                });

            modelBuilder.Entity("TNO.Entities.ContentTopic", b =>
                {
                    b.Property<long>("ContentId")
                        .HasColumnType("bigint")
                        .HasColumnName("content_id");

                    b.Property<int>("TopicId")
                        .HasColumnType("integer")
                        .HasColumnName("topic_id");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("character varying(250)")
                        .HasColumnName("created_by");

                    b.Property<DateTime>("CreatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_on")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<int>("Score")
                        .HasColumnType("integer")
                        .HasColumnName("score");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("character varying(250)")
                        .HasColumnName("updated_by");

                    b.Property<DateTime>("UpdatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_on")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<long>("Version")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasColumnName("version")
                        .HasDefaultValueSql("0");

                    b.HasKey("ContentId", "TopicId");

                    b.HasIndex("TopicId");

                    b.ToTable("content_topic");
                });

            modelBuilder.Entity("TNO.Entities.ContentTypeAction", b =>
                {
                    b.Property<int>("ContentType")
                        .HasColumnType("integer")
                        .HasColumnName("content_type");

                    b.Property<int>("ActionId")
                        .HasColumnType("integer")
                        .HasColumnName("action_id");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("character varying(250)")
                        .HasColumnName("created_by");

                    b.Property<DateTime>("CreatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_on")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("character varying(250)")
                        .HasColumnName("updated_by");

                    b.Property<DateTime>("UpdatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_on")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<long>("Version")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasColumnName("version")
                        .HasDefaultValueSql("0");

                    b.HasKey("ContentType", "ActionId");

                    b.HasIndex("ActionId");

                    b.ToTable("content_type_action");
                });

            modelBuilder.Entity("TNO.Entities.Contributor", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("Aliases")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)")
                        .HasColumnName("aliases");

                    b.Property<bool>("AutoTranscribe")
                        .HasColumnType("boolean")
                        .HasColumnName("auto_transcribe");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("character varying(250)")
                        .HasColumnName("created_by");

                    b.Property<DateTime>("CreatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_on")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<string>("Description")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)")
                        .HasColumnName("description")
                        .HasDefaultValueSql("''");

                    b.Property<bool>("IsEnabled")
                        .HasColumnType("boolean")
                        .HasColumnName("is_enabled");

                    b.Property<bool>("IsPress")
                        .HasColumnType("boolean")
                        .HasColumnName("is_press");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("name");

                    b.Property<int>("SortOrder")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(0)
                        .HasColumnName("sort_order");

                    b.Property<int?>("SourceId")
                        .HasColumnType("integer")
                        .HasColumnName("source_id");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("character varying(250)")
                        .HasColumnName("updated_by");

                    b.Property<DateTime>("UpdatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_on")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<long>("Version")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasColumnName("version")
                        .HasDefaultValueSql("0");

                    b.HasKey("Id");

                    b.HasIndex("SourceId");

                    b.HasIndex(new[] { "IsEnabled", "Name" }, "IX_contributor_is_enabled");

                    b.ToTable("contributor");
                });

            modelBuilder.Entity("TNO.Entities.DataLocation", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<int?>("ConnectionId")
                        .HasColumnType("integer")
                        .HasColumnName("connection_id");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("character varying(250)")
                        .HasColumnName("created_by");

                    b.Property<DateTime>("CreatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_on")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<string>("Description")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)")
                        .HasColumnName("description")
                        .HasDefaultValueSql("''");

                    b.Property<bool>("IsEnabled")
                        .HasColumnType("boolean")
                        .HasColumnName("is_enabled");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("name");

                    b.Property<int>("SortOrder")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(0)
                        .HasColumnName("sort_order");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("character varying(250)")
                        .HasColumnName("updated_by");

                    b.Property<DateTime>("UpdatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_on")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<long>("Version")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasColumnName("version")
                        .HasDefaultValueSql("0");

                    b.HasKey("Id");

                    b.HasIndex("ConnectionId");

                    b.HasIndex(new[] { "IsEnabled", "Name" }, "IX_datalocation_is_enabled");

                    b.HasIndex(new[] { "Name" }, "IX_name")
                        .IsUnique()
                        .HasDatabaseName("IX_name2");

                    b.ToTable("data_location");
                });

            modelBuilder.Entity("TNO.Entities.EarnedMedia", b =>
                {
                    b.Property<int>("SourceId")
                        .HasColumnType("integer")
                        .HasColumnName("source_id");

                    b.Property<int>("ContentType")
                        .HasColumnType("integer")
                        .HasColumnName("content_type");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("character varying(250)")
                        .HasColumnName("created_by");

                    b.Property<DateTime>("CreatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_on")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<int>("LengthOfContent")
                        .HasColumnType("integer")
                        .HasColumnName("length_of_content");

                    b.Property<float>("Rate")
                        .HasColumnType("real")
                        .HasColumnName("rate");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("character varying(250)")
                        .HasColumnName("updated_by");

                    b.Property<DateTime>("UpdatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_on")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<long>("Version")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasColumnName("version")
                        .HasDefaultValueSql("0");

                    b.HasKey("SourceId", "ContentType");

                    b.ToTable("earned_media");
                });

            modelBuilder.Entity("TNO.Entities.EventSchedule", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("character varying(250)")
                        .HasColumnName("created_by");

                    b.Property<DateTime>("CreatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_on")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)")
                        .HasColumnName("description");

                    b.Property<int>("EventType")
                        .HasColumnType("integer")
                        .HasColumnName("event_type");

                    b.Property<int?>("FolderId")
                        .HasColumnType("integer")
                        .HasColumnName("folder_id");

                    b.Property<bool>("IsEnabled")
                        .HasColumnType("boolean")
                        .HasColumnName("is_enabled");

                    b.Property<DateTime?>("LastRanOn")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("last_ran_on");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("name");

                    b.Property<int?>("NotificationId")
                        .HasColumnType("integer")
                        .HasColumnName("notification_id");

                    b.Property<int?>("ReportId")
                        .HasColumnType("integer")
                        .HasColumnName("report_id");

                    b.Property<DateTime?>("RequestSentOn")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("request_sent_on");

                    b.Property<int>("ScheduleId")
                        .HasColumnType("integer")
                        .HasColumnName("schedule_id");

                    b.Property<JsonDocument>("Settings")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("jsonb")
                        .HasColumnName("settings")
                        .HasDefaultValueSql("'{}'::jsonb");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("character varying(250)")
                        .HasColumnName("updated_by");

                    b.Property<DateTime>("UpdatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_on")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<long>("Version")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasColumnName("version")
                        .HasDefaultValueSql("0");

                    b.HasKey("Id");

                    b.HasIndex("FolderId");

                    b.HasIndex("NotificationId");

                    b.HasIndex("ReportId");

                    b.HasIndex("ScheduleId");

                    b.ToTable("event_schedule");
                });

            modelBuilder.Entity("TNO.Entities.FileReference", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<long>("Id"));

                    b.Property<long>("ContentId")
                        .HasColumnType("bigint")
                        .HasColumnName("content_id");

                    b.Property<string>("ContentType")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("content_type");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("character varying(250)")
                        .HasColumnName("created_by");

                    b.Property<DateTime>("CreatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_on")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<string>("FileName")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)")
                        .HasColumnName("file_name");

                    b.Property<bool>("IsSyncedToS3")
                        .HasColumnType("boolean")
                        .HasColumnName("is_synced_to_s3");

                    b.Property<bool>("IsUploaded")
                        .HasColumnType("boolean")
                        .HasColumnName("is_uploaded");

                    b.Property<DateTime?>("LastSyncedToS3On")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("last_synced_to_s3_on");

                    b.Property<string>("Path")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)")
                        .HasColumnName("path");

                    b.Property<long>("RunningTime")
                        .HasColumnType("bigint")
                        .HasColumnName("running_time");

                    b.Property<string>("S3Path")
                        .HasColumnType("text")
                        .HasColumnName("s3_path");

                    b.Property<long>("Size")
                        .HasColumnType("bigint")
                        .HasColumnName("size");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("character varying(250)")
                        .HasColumnName("updated_by");

                    b.Property<DateTime>("UpdatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_on")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<long>("Version")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasColumnName("version")
                        .HasDefaultValueSql("0");

                    b.HasKey("Id");

                    b.HasIndex("ContentId");

                    b.ToTable("file_reference");
                });

            modelBuilder.Entity("TNO.Entities.Filter", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("character varying(250)")
                        .HasColumnName("created_by");

                    b.Property<DateTime>("CreatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_on")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<string>("Description")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)")
                        .HasColumnName("description")
                        .HasDefaultValueSql("''");

                    b.Property<bool>("IsEnabled")
                        .HasColumnType("boolean")
                        .HasColumnName("is_enabled");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("name");

                    b.Property<int?>("OwnerId")
                        .HasColumnType("integer")
                        .HasColumnName("owner_id");

                    b.Property<JsonDocument>("Query")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("jsonb")
                        .HasColumnName("query")
                        .HasDefaultValueSql("'{}'::jsonb");

                    b.Property<JsonDocument>("Settings")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("jsonb")
                        .HasColumnName("settings")
                        .HasDefaultValueSql("'{}'::jsonb");

                    b.Property<int>("SortOrder")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(0)
                        .HasColumnName("sort_order");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("character varying(250)")
                        .HasColumnName("updated_by");

                    b.Property<DateTime>("UpdatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_on")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<long>("Version")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasColumnName("version")
                        .HasDefaultValueSql("0");

                    b.HasKey("Id");

                    b.HasIndex("OwnerId", "Name")
                        .IsUnique();

                    b.HasIndex(new[] { "IsEnabled", "Name" }, "IX_filter_is_enabled");

                    b.ToTable("filter");
                });

            modelBuilder.Entity("TNO.Entities.Folder", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("character varying(250)")
                        .HasColumnName("created_by");

                    b.Property<DateTime>("CreatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_on")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<string>("Description")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)")
                        .HasColumnName("description")
                        .HasDefaultValueSql("''");

                    b.Property<int?>("FilterId")
                        .HasColumnType("integer")
                        .HasColumnName("filter_id");

                    b.Property<bool>("IsEnabled")
                        .HasColumnType("boolean")
                        .HasColumnName("is_enabled");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("name");

                    b.Property<int?>("OwnerId")
                        .HasColumnType("integer")
                        .HasColumnName("owner_id");

                    b.Property<JsonDocument>("Settings")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("jsonb")
                        .HasColumnName("settings")
                        .HasDefaultValueSql("'{}'::jsonb");

                    b.Property<int>("SortOrder")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(0)
                        .HasColumnName("sort_order");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("character varying(250)")
                        .HasColumnName("updated_by");

                    b.Property<DateTime>("UpdatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_on")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<long>("Version")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasColumnName("version")
                        .HasDefaultValueSql("0");

                    b.HasKey("Id");

                    b.HasIndex("FilterId");

                    b.HasIndex("OwnerId", "Name")
                        .IsUnique();

                    b.HasIndex(new[] { "IsEnabled", "Name" }, "IX_folder_is_enabled");

                    b.ToTable("folder");
                });

            modelBuilder.Entity("TNO.Entities.FolderContent", b =>
                {
                    b.Property<int>("FolderId")
                        .HasColumnType("integer")
                        .HasColumnName("folder_id");

                    b.Property<long>("ContentId")
                        .HasColumnType("bigint")
                        .HasColumnName("content_id");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("character varying(250)")
                        .HasColumnName("created_by");

                    b.Property<DateTime>("CreatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_on")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<int>("SortOrder")
                        .HasColumnType("integer")
                        .HasColumnName("sort_order");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("character varying(250)")
                        .HasColumnName("updated_by");

                    b.Property<DateTime>("UpdatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_on")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<long>("Version")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasColumnName("version")
                        .HasDefaultValueSql("0");

                    b.HasKey("FolderId", "ContentId");

                    b.HasIndex("ContentId");

                    b.ToTable("folder_content");
                });

            modelBuilder.Entity("TNO.Entities.Ingest", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<JsonDocument>("Configuration")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("jsonb")
                        .HasColumnName("configuration")
                        .HasDefaultValueSql("'{}'::jsonb");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("character varying(250)")
                        .HasColumnName("created_by");

                    b.Property<DateTime>("CreatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_on")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<string>("Description")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)")
                        .HasColumnName("description")
                        .HasDefaultValueSql("''");

                    b.Property<int>("DestinationConnectionId")
                        .HasColumnType("integer")
                        .HasColumnName("destination_connection_id");

                    b.Property<int>("IngestTypeId")
                        .HasColumnType("integer")
                        .HasColumnName("ingest_type_id");

                    b.Property<bool>("IsEnabled")
                        .HasColumnType("boolean")
                        .HasColumnName("is_enabled");

                    b.Property<int>("MediaTypeId")
                        .HasColumnType("integer")
                        .HasColumnName("media_type_id");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("name");

                    b.Property<int>("ResetRetryAfterDelayMs")
                        .HasColumnType("integer")
                        .HasColumnName("reset_retry_after_delay_ms");

                    b.Property<int>("RetryLimit")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(3)
                        .HasColumnName("retry_limit");

                    b.Property<int>("ScheduleType")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(0)
                        .HasColumnName("schedule_type");

                    b.Property<int>("SourceConnectionId")
                        .HasColumnType("integer")
                        .HasColumnName("source_connection_id");

                    b.Property<int>("SourceId")
                        .HasColumnType("integer")
                        .HasColumnName("source_id");

                    b.Property<string>("Topic")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("topic");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("character varying(250)")
                        .HasColumnName("updated_by");

                    b.Property<DateTime>("UpdatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_on")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<long>("Version")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasColumnName("version")
                        .HasDefaultValueSql("0");

                    b.HasKey("Id");

                    b.HasIndex("DestinationConnectionId");

                    b.HasIndex("MediaTypeId");

                    b.HasIndex("SourceConnectionId");

                    b.HasIndex("SourceId");

                    b.HasIndex(new[] { "IngestTypeId", "SourceId", "Topic" }, "IX_ingest");

                    b.HasIndex(new[] { "Name" }, "IX_name")
                        .IsUnique()
                        .HasDatabaseName("IX_name3");

                    b.ToTable("ingest");
                });

            modelBuilder.Entity("TNO.Entities.IngestDataLocation", b =>
                {
                    b.Property<int>("IngestId")
                        .HasColumnType("integer")
                        .HasColumnName("ingest_id");

                    b.Property<int>("DataLocationId")
                        .HasColumnType("integer")
                        .HasColumnName("data_location_id");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("character varying(250)")
                        .HasColumnName("created_by");

                    b.Property<DateTime>("CreatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_on")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("character varying(250)")
                        .HasColumnName("updated_by");

                    b.Property<DateTime>("UpdatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_on")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<long>("Version")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasColumnName("version")
                        .HasDefaultValueSql("0");

                    b.HasKey("IngestId", "DataLocationId");

                    b.HasIndex("DataLocationId");

                    b.ToTable("ingest_data_location");
                });

            modelBuilder.Entity("TNO.Entities.IngestSchedule", b =>
                {
                    b.Property<int>("IngestId")
                        .HasColumnType("integer")
                        .HasColumnName("ingest_id");

                    b.Property<int>("ScheduleId")
                        .HasColumnType("integer")
                        .HasColumnName("schedule_id");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("character varying(250)")
                        .HasColumnName("created_by");

                    b.Property<DateTime>("CreatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_on")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("character varying(250)")
                        .HasColumnName("updated_by");

                    b.Property<DateTime>("UpdatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_on")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<long>("Version")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasColumnName("version")
                        .HasDefaultValueSql("0");

                    b.HasKey("IngestId", "ScheduleId");

                    b.HasIndex("ScheduleId");

                    b.ToTable("ingest_schedule");
                });

            modelBuilder.Entity("TNO.Entities.IngestState", b =>
                {
                    b.Property<int>("IngestId")
                        .HasColumnType("integer")
                        .HasColumnName("ingest_id");

                    b.Property<DateTime?>("CreationDateOfLastItem")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("creation_date_of_last_item");

                    b.Property<int>("FailedAttempts")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(0)
                        .HasColumnName("failed_attempts");

                    b.Property<DateTime?>("LastRanOn")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("last_ran_on");

                    b.HasKey("IngestId");

                    b.ToTable("ingest_state");
                });

            modelBuilder.Entity("TNO.Entities.IngestType", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<bool>("AutoTranscribe")
                        .HasColumnType("boolean")
                        .HasColumnName("auto_transcribe");

                    b.Property<int>("ContentType")
                        .HasColumnType("integer")
                        .HasColumnName("content_type");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("character varying(250)")
                        .HasColumnName("created_by");

                    b.Property<DateTime>("CreatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_on")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<string>("Description")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)")
                        .HasColumnName("description")
                        .HasDefaultValueSql("''");

                    b.Property<bool>("DisableTranscribe")
                        .HasColumnType("boolean")
                        .HasColumnName("disable_transcribe");

                    b.Property<bool>("IsEnabled")
                        .HasColumnType("boolean")
                        .HasColumnName("is_enabled");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("name");

                    b.Property<int>("SortOrder")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(0)
                        .HasColumnName("sort_order");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("character varying(250)")
                        .HasColumnName("updated_by");

                    b.Property<DateTime>("UpdatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_on")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<long>("Version")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasColumnName("version")
                        .HasDefaultValueSql("0");

                    b.HasKey("Id");

                    b.HasIndex(new[] { "IsEnabled", "Name" }, "IX_ingesttype_is_enabled");

                    b.HasIndex(new[] { "Name" }, "IX_name")
                        .IsUnique()
                        .HasDatabaseName("IX_name4");

                    b.ToTable("ingest_type");
                });

            modelBuilder.Entity("TNO.Entities.License", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("character varying(250)")
                        .HasColumnName("created_by");

                    b.Property<DateTime>("CreatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_on")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<string>("Description")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)")
                        .HasColumnName("description")
                        .HasDefaultValueSql("''");

                    b.Property<bool>("IsEnabled")
                        .HasColumnType("boolean")
                        .HasColumnName("is_enabled");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("name");

                    b.Property<int>("SortOrder")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(0)
                        .HasColumnName("sort_order");

                    b.Property<int>("TTL")
                        .HasColumnType("integer")
                        .HasColumnName("ttl");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("character varying(250)")
                        .HasColumnName("updated_by");

                    b.Property<DateTime>("UpdatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_on")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<long>("Version")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasColumnName("version")
                        .HasDefaultValueSql("0");

                    b.HasKey("Id");

                    b.HasIndex(new[] { "IsEnabled", "Name" }, "IX_license_is_enabled");

                    b.HasIndex(new[] { "Name" }, "IX_name")
                        .IsUnique()
                        .HasDatabaseName("IX_name5");

                    b.ToTable("license");
                });

            modelBuilder.Entity("TNO.Entities.MediaAnalytics", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<float>("AgeGroup1")
                        .HasColumnType("real")
                        .HasColumnName("age_group1");

                    b.Property<string>("AgeGroup1Label")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("character varying(250)")
                        .HasColumnName("age_group1_label");

                    b.Property<float>("AgeGroup2")
                        .HasColumnType("real")
                        .HasColumnName("age_group2");

                    b.Property<string>("AgeGroup2Label")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("character varying(250)")
                        .HasColumnName("age_group2_label");

                    b.Property<float>("AgeGroup3")
                        .HasColumnType("real")
                        .HasColumnName("age_group3");

                    b.Property<string>("AgeGroup3Label")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("character varying(250)")
                        .HasColumnName("age_group3_label");

                    b.Property<float>("AgeGroup4")
                        .HasColumnType("real")
                        .HasColumnName("age_group4");

                    b.Property<string>("AgeGroup4Label")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("character varying(250)")
                        .HasColumnName("age_group4_label");

                    b.Property<float>("AverageViews")
                        .HasColumnType("real")
                        .HasColumnName("average_views");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("character varying(250)")
                        .HasColumnName("created_by");

                    b.Property<DateTime>("CreatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_on")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<string>("Description")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)")
                        .HasColumnName("description")
                        .HasDefaultValueSql("''");

                    b.Property<bool>("IsEnabled")
                        .HasColumnType("boolean")
                        .HasColumnName("is_enabled");

                    b.Property<float>("MaleViewers")
                        .HasColumnType("real")
                        .HasColumnName("male_viewers");

                    b.Property<int>("MediaTypeId")
                        .HasColumnType("integer")
                        .HasColumnName("media_type_id");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("name");

                    b.Property<float>("PageViews1")
                        .HasColumnType("real")
                        .HasColumnName("page_views1");

                    b.Property<string>("PageViews1Label")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("character varying(250)")
                        .HasColumnName("page_views1_label");

                    b.Property<float>("PageViews2")
                        .HasColumnType("real")
                        .HasColumnName("page_views2");

                    b.Property<float>("PageViews3")
                        .HasColumnType("real")
                        .HasColumnName("page_views3");

                    b.Property<float>("PageViews4")
                        .HasColumnType("real")
                        .HasColumnName("page_views4");

                    b.Property<string>("Page_Views2_Label")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("character varying(250)")
                        .HasColumnName("page_views2_label");

                    b.Property<string>("Page_Views3_Label")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("character varying(250)")
                        .HasColumnName("page_views3_label");

                    b.Property<string>("Page_Views4_Label")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("character varying(250)")
                        .HasColumnName("page_views4_label");

                    b.Property<DateTime>("PublishedOn")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("published_on");

                    b.Property<int>("SortOrder")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(0)
                        .HasColumnName("sort_order");

                    b.Property<int>("SourceId")
                        .HasColumnType("integer")
                        .HasColumnName("source_id");

                    b.Property<int>("TotalViews")
                        .HasColumnType("integer")
                        .HasColumnName("total_views");

                    b.Property<int>("UniqueViews")
                        .HasColumnType("integer")
                        .HasColumnName("unique_views");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("character varying(250)")
                        .HasColumnName("updated_by");

                    b.Property<DateTime>("UpdatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_on")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<long>("Version")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasColumnName("version")
                        .HasDefaultValueSql("0");

                    b.Property<float>("WatchTime1")
                        .HasColumnType("real")
                        .HasColumnName("watch_time1");

                    b.Property<string>("WatchTime1Label")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("character varying(250)")
                        .HasColumnName("watch_time1_label");

                    b.Property<float>("WatchTime2")
                        .HasColumnType("real")
                        .HasColumnName("watch_time2");

                    b.Property<string>("WatchTime2Label")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("character varying(250)")
                        .HasColumnName("watch_time2_label");

                    b.Property<float>("WatchTime3")
                        .HasColumnType("real")
                        .HasColumnName("watch_time3");

                    b.Property<string>("WatchTime3Label")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("character varying(250)")
                        .HasColumnName("watch_time3_label");

                    b.Property<float>("WatchTime4")
                        .HasColumnType("real")
                        .HasColumnName("watch_time4");

                    b.Property<string>("WatchTime4Label")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("character varying(250)")
                        .HasColumnName("watch_time4_label");

                    b.HasKey("Id");

                    b.HasIndex("MediaTypeId");

                    b.HasIndex("SourceId");

                    b.HasIndex("PublishedOn", "SourceId", "MediaTypeId")
                        .IsUnique();

                    b.HasIndex(new[] { "IsEnabled", "Name" }, "IX_mediaanalytics_is_enabled");

                    b.ToTable("media_analytics");
                });

            modelBuilder.Entity("TNO.Entities.MediaType", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<bool>("AutoTranscribe")
                        .HasColumnType("boolean")
                        .HasColumnName("auto_transcribe");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("character varying(250)")
                        .HasColumnName("created_by");

                    b.Property<DateTime>("CreatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_on")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<string>("Description")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)")
                        .HasColumnName("description")
                        .HasDefaultValueSql("''");

                    b.Property<bool>("IsEnabled")
                        .HasColumnType("boolean")
                        .HasColumnName("is_enabled");

                    b.Property<int>("ListOption")
                        .HasColumnType("integer")
                        .HasColumnName("list_option");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("name");

                    b.Property<JsonDocument>("Settings")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("jsonb")
                        .HasDefaultValueSql("'{}'::jsonb");

                    b.Property<int>("SortOrder")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(0)
                        .HasColumnName("sort_order");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("character varying(250)")
                        .HasColumnName("updated_by");

                    b.Property<DateTime>("UpdatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_on")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<long>("Version")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasColumnName("version")
                        .HasDefaultValueSql("0");

                    b.HasKey("Id");

                    b.HasIndex(new[] { "IsEnabled", "Name" }, "IX_mediatype_is_enabled");

                    b.HasIndex(new[] { "Name" }, "IX_name")
                        .IsUnique()
                        .HasDatabaseName("IX_name6");

                    b.ToTable("media_type");
                });

            modelBuilder.Entity("TNO.Entities.Metric", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("character varying(250)")
                        .HasColumnName("created_by");

                    b.Property<DateTime>("CreatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_on")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<string>("Description")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)")
                        .HasColumnName("description")
                        .HasDefaultValueSql("''");

                    b.Property<bool>("IsEnabled")
                        .HasColumnType("boolean")
                        .HasColumnName("is_enabled");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("name");

                    b.Property<int>("SortOrder")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(0)
                        .HasColumnName("sort_order");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("character varying(250)")
                        .HasColumnName("updated_by");

                    b.Property<DateTime>("UpdatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_on")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<long>("Version")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasColumnName("version")
                        .HasDefaultValueSql("0");

                    b.HasKey("Id");

                    b.HasIndex(new[] { "IsEnabled", "Name" }, "IX_metric_is_enabled");

                    b.HasIndex(new[] { "Name" }, "IX_name")
                        .IsUnique()
                        .HasDatabaseName("IX_name7");

                    b.ToTable("metric");
                });

            modelBuilder.Entity("TNO.Entities.Minister", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("Aliases")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(250)
                        .HasColumnType("character varying(250)")
                        .HasColumnName("aliases")
                        .HasDefaultValueSql("''");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("character varying(250)")
                        .HasColumnName("created_by");

                    b.Property<DateTime>("CreatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_on")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<string>("Description")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)")
                        .HasColumnName("description")
                        .HasDefaultValueSql("''");

                    b.Property<bool>("IsEnabled")
                        .HasColumnType("boolean")
                        .HasColumnName("is_enabled");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("name");

                    b.Property<int?>("OrganizationId")
                        .HasColumnType("integer")
                        .HasColumnName("organization_id");

                    b.Property<string>("Position")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(250)
                        .HasColumnType("character varying(250)")
                        .HasColumnName("position")
                        .HasDefaultValueSql("''");

                    b.Property<int>("SortOrder")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(0)
                        .HasColumnName("sort_order");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("character varying(250)")
                        .HasColumnName("updated_by");

                    b.Property<DateTime>("UpdatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_on")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<long>("Version")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasColumnName("version")
                        .HasDefaultValueSql("0");

                    b.HasKey("Id");

                    b.HasIndex("Name")
                        .IsUnique();

                    b.HasIndex("OrganizationId");

                    b.HasIndex(new[] { "IsEnabled", "Name" }, "IX_minister_is_enabled");

                    b.ToTable("minister");
                });

            modelBuilder.Entity("TNO.Entities.Notification", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<bool>("AlertOnIndex")
                        .HasColumnType("boolean")
                        .HasColumnName("alert_on_index");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("character varying(250)")
                        .HasColumnName("created_by");

                    b.Property<DateTime>("CreatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_on")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<string>("Description")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)")
                        .HasColumnName("description")
                        .HasDefaultValueSql("''");

                    b.Property<bool>("IsEnabled")
                        .HasColumnType("boolean")
                        .HasColumnName("is_enabled");

                    b.Property<bool>("IsPublic")
                        .HasColumnType("boolean")
                        .HasColumnName("is_public");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("name");

                    b.Property<int>("NotificationType")
                        .HasColumnType("integer")
                        .HasColumnName("notification_type");

                    b.Property<int?>("OwnerId")
                        .HasColumnType("integer")
                        .HasColumnName("owner_id");

                    b.Property<JsonDocument>("Query")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("jsonb")
                        .HasColumnName("query")
                        .HasDefaultValueSql("'{}'::jsonb");

                    b.Property<int>("Resend")
                        .HasColumnType("integer")
                        .HasColumnName("resend");

                    b.Property<JsonDocument>("Settings")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("jsonb")
                        .HasColumnName("settings")
                        .HasDefaultValueSql("'{}'::jsonb");

                    b.Property<int>("SortOrder")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(0)
                        .HasColumnName("sort_order");

                    b.Property<int>("TemplateId")
                        .HasColumnType("integer")
                        .HasColumnName("notification_template_id");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("character varying(250)")
                        .HasColumnName("updated_by");

                    b.Property<DateTime>("UpdatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_on")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<long>("Version")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasColumnName("version")
                        .HasDefaultValueSql("0");

                    b.HasKey("Id");

                    b.HasIndex("TemplateId");

                    b.HasIndex("OwnerId", "Name")
                        .IsUnique();

                    b.HasIndex(new[] { "IsEnabled", "Name" }, "IX_notification_is_enabled");

                    b.ToTable("notification");
                });

            modelBuilder.Entity("TNO.Entities.NotificationInstance", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<long>("Id"));

                    b.Property<string>("Body")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("body");

                    b.Property<long>("ContentId")
                        .HasColumnType("bigint")
                        .HasColumnName("content_id");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("character varying(250)")
                        .HasColumnName("created_by");

                    b.Property<DateTime>("CreatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_on")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<int>("NotificationId")
                        .HasColumnType("integer")
                        .HasColumnName("notification_id");

                    b.Property<int?>("OwnerId")
                        .HasColumnType("integer")
                        .HasColumnName("owner_id");

                    b.Property<JsonDocument>("Response")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("jsonb")
                        .HasColumnName("response")
                        .HasDefaultValueSql("'{}'::jsonb");

                    b.Property<DateTime?>("SentOn")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("sent_on");

                    b.Property<int>("Status")
                        .HasColumnType("integer")
                        .HasColumnName("status");

                    b.Property<string>("Subject")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("subject");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("character varying(250)")
                        .HasColumnName("updated_by");

                    b.Property<DateTime>("UpdatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_on")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<long>("Version")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasColumnName("version")
                        .HasDefaultValueSql("0");

                    b.HasKey("Id");

                    b.HasIndex("ContentId");

                    b.HasIndex("NotificationId");

                    b.HasIndex("Status", "SentOn");

                    b.ToTable("notification_instance");
                });

            modelBuilder.Entity("TNO.Entities.NotificationTemplate", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("Body")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("body");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("character varying(250)")
                        .HasColumnName("created_by");

                    b.Property<DateTime>("CreatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_on")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<string>("Description")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)")
                        .HasColumnName("description")
                        .HasDefaultValueSql("''");

                    b.Property<bool>("IsEnabled")
                        .HasColumnType("boolean")
                        .HasColumnName("is_enabled");

                    b.Property<bool>("IsPublic")
                        .HasColumnType("boolean")
                        .HasColumnName("is_public");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("name");

                    b.Property<JsonDocument>("Settings")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("jsonb")
                        .HasColumnName("settings")
                        .HasDefaultValueSql("'{}'::jsonb");

                    b.Property<int>("SortOrder")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(0)
                        .HasColumnName("sort_order");

                    b.Property<string>("Subject")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("subject");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("character varying(250)")
                        .HasColumnName("updated_by");

                    b.Property<DateTime>("UpdatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_on")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<long>("Version")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasColumnName("version")
                        .HasDefaultValueSql("0");

                    b.HasKey("Id");

                    b.HasIndex("Name")
                        .IsUnique();

                    b.HasIndex("IsPublic", "IsEnabled");

                    b.HasIndex(new[] { "IsEnabled", "Name" }, "IX_notificationtemplate_is_enabled");

                    b.ToTable("notification_template");
                });

            modelBuilder.Entity("TNO.Entities.Organization", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("character varying(250)")
                        .HasColumnName("created_by");

                    b.Property<DateTime>("CreatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_on")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<string>("Description")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)")
                        .HasColumnName("description")
                        .HasDefaultValueSql("''");

                    b.Property<bool>("IsEnabled")
                        .HasColumnType("boolean")
                        .HasColumnName("is_enabled");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("name");

                    b.Property<int?>("ParentId")
                        .HasColumnType("integer")
                        .HasColumnName("parent_id");

                    b.Property<int>("SortOrder")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(0)
                        .HasColumnName("sort_order");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("character varying(250)")
                        .HasColumnName("updated_by");

                    b.Property<DateTime>("UpdatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_on")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<long>("Version")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasColumnName("version")
                        .HasDefaultValueSql("0");

                    b.HasKey("Id");

                    b.HasIndex("ParentId", "Name")
                        .IsUnique();

                    b.HasIndex(new[] { "IsEnabled", "Name" }, "IX_organization_is_enabled");

                    b.ToTable("organization");
                });

            modelBuilder.Entity("TNO.Entities.Product", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("character varying(250)")
                        .HasColumnName("created_by");

                    b.Property<DateTime>("CreatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_on")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<string>("Description")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)")
                        .HasColumnName("description")
                        .HasDefaultValueSql("''");

                    b.Property<bool>("IsEnabled")
                        .HasColumnType("boolean")
                        .HasColumnName("is_enabled");

                    b.Property<bool>("IsPublic")
                        .HasColumnType("boolean")
                        .HasColumnName("is_public");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("name");

                    b.Property<int>("ProductType")
                        .HasColumnType("integer")
                        .HasColumnName("product_type");

                    b.Property<int>("SortOrder")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(0)
                        .HasColumnName("sort_order");

                    b.Property<int>("TargetProductId")
                        .HasColumnType("integer")
                        .HasColumnName("target_product_id");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("character varying(250)")
                        .HasColumnName("updated_by");

                    b.Property<DateTime>("UpdatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_on")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<long>("Version")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasColumnName("version")
                        .HasDefaultValueSql("0");

                    b.HasKey("Id");

                    b.HasIndex("Name", "TargetProductId", "ProductType")
                        .IsUnique();

                    b.HasIndex(new[] { "IsEnabled", "Name" }, "IX_product_is_enabled");

                    b.ToTable("product");
                });

            modelBuilder.Entity("TNO.Entities.Quote", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<long>("Id"));

                    b.Property<string>("Byline")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)")
                        .HasColumnName("byline");

                    b.Property<long>("ContentId")
                        .HasColumnType("bigint")
                        .HasColumnName("content_id");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("character varying(250)")
                        .HasColumnName("created_by");

                    b.Property<DateTime>("CreatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_on")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<bool>("IsRelevant")
                        .HasColumnType("boolean")
                        .HasColumnName("is_relevant");

                    b.Property<string>("Statement")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("statement");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("character varying(250)")
                        .HasColumnName("updated_by");

                    b.Property<DateTime>("UpdatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_on")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<long>("Version")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasColumnName("version")
                        .HasDefaultValueSql("0");

                    b.HasKey("Id");

                    b.HasIndex("ContentId");

                    b.HasIndex(new[] { "Statement" }, "IX_statement");

                    b.ToTable("quote");
                });

            modelBuilder.Entity("TNO.Entities.Report", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("character varying(250)")
                        .HasColumnName("created_by");

                    b.Property<DateTime>("CreatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_on")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<string>("Description")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)")
                        .HasColumnName("description")
                        .HasDefaultValueSql("''");

                    b.Property<bool>("IsEnabled")
                        .HasColumnType("boolean")
                        .HasColumnName("is_enabled");

                    b.Property<bool>("IsPublic")
                        .HasColumnType("boolean")
                        .HasColumnName("is_public");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("name");

                    b.Property<int?>("OwnerId")
                        .HasColumnType("integer")
                        .HasColumnName("owner_id");

                    b.Property<JsonDocument>("Settings")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("jsonb")
                        .HasColumnName("settings")
                        .HasDefaultValueSql("'{}'::jsonb");

                    b.Property<int>("SortOrder")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(0)
                        .HasColumnName("sort_order");

                    b.Property<int>("TemplateId")
                        .HasColumnType("integer")
                        .HasColumnName("report_template_id");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("character varying(250)")
                        .HasColumnName("updated_by");

                    b.Property<DateTime>("UpdatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_on")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<long>("Version")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasColumnName("version")
                        .HasDefaultValueSql("0");

                    b.HasKey("Id");

                    b.HasIndex("TemplateId");

                    b.HasIndex("OwnerId", "Name")
                        .IsUnique();

                    b.HasIndex(new[] { "IsEnabled", "Name" }, "IX_report_is_enabled");

                    b.ToTable("report");
                });

            modelBuilder.Entity("TNO.Entities.ReportInstance", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<long>("Id"));

                    b.Property<string>("Body")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("body");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("character varying(250)")
                        .HasColumnName("created_by");

                    b.Property<DateTime>("CreatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_on")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<int?>("OwnerId")
                        .HasColumnType("integer")
                        .HasColumnName("owner_id");

                    b.Property<DateTime?>("PublishedOn")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("published_on");

                    b.Property<int>("ReportId")
                        .HasColumnType("integer")
                        .HasColumnName("report_id");

                    b.Property<JsonDocument>("Response")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("jsonb")
                        .HasColumnName("response")
                        .HasDefaultValueSql("'{}'::jsonb");

                    b.Property<DateTime?>("SentOn")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("sent_on");

                    b.Property<int>("Status")
                        .HasColumnType("integer")
                        .HasColumnName("status");

                    b.Property<string>("Subject")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("subject");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("character varying(250)")
                        .HasColumnName("updated_by");

                    b.Property<DateTime>("UpdatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_on")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<long>("Version")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasColumnName("version")
                        .HasDefaultValueSql("0");

                    b.HasKey("Id");

                    b.HasIndex("OwnerId");

                    b.HasIndex("ReportId");

                    b.HasIndex(new[] { "PublishedOn", "CreatedOn" }, "IX_report_dates");

                    b.ToTable("report_instance");
                });

            modelBuilder.Entity("TNO.Entities.ReportInstanceContent", b =>
                {
                    b.Property<long>("InstanceId")
                        .HasColumnType("bigint")
                        .HasColumnName("report_instance_id");

                    b.Property<long>("ContentId")
                        .HasColumnType("bigint")
                        .HasColumnName("content_id");

                    b.Property<string>("SectionName")
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("section_name")
                        .HasDefaultValueSql("''");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("character varying(250)")
                        .HasColumnName("created_by");

                    b.Property<DateTime>("CreatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_on")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<int>("SortOrder")
                        .HasColumnType("integer")
                        .HasColumnName("sort_order");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("character varying(250)")
                        .HasColumnName("updated_by");

                    b.Property<DateTime>("UpdatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_on")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<long>("Version")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasColumnName("version")
                        .HasDefaultValueSql("0");

                    b.HasKey("InstanceId", "ContentId", "SectionName");

                    b.HasIndex("ContentId");

                    b.ToTable("report_instance_content");
                });

            modelBuilder.Entity("TNO.Entities.ReportSection", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("character varying(250)")
                        .HasColumnName("created_by");

                    b.Property<DateTime>("CreatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_on")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<string>("Description")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("text")
                        .HasColumnName("description")
                        .HasDefaultValueSql("''");

                    b.Property<int?>("FilterId")
                        .HasColumnType("integer")
                        .HasColumnName("filter_id");

                    b.Property<int?>("FolderId")
                        .HasColumnType("integer")
                        .HasColumnName("folder_id");

                    b.Property<bool>("IsEnabled")
                        .HasColumnType("boolean")
                        .HasColumnName("is_enabled");

                    b.Property<int?>("LinkedReportId")
                        .HasColumnType("integer")
                        .HasColumnName("linked_report_id");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("name");

                    b.Property<int>("ReportId")
                        .HasColumnType("integer")
                        .HasColumnName("report_id");

                    b.Property<int>("SectionType")
                        .HasColumnType("integer")
                        .HasColumnName("section_type");

                    b.Property<JsonDocument>("Settings")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("jsonb")
                        .HasColumnName("settings")
                        .HasDefaultValueSql("'{}'::jsonb");

                    b.Property<int>("SortOrder")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(0)
                        .HasColumnName("sort_order");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("character varying(250)")
                        .HasColumnName("updated_by");

                    b.Property<DateTime>("UpdatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_on")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<long>("Version")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasColumnName("version")
                        .HasDefaultValueSql("0");

                    b.HasKey("Id");

                    b.HasIndex("FilterId");

                    b.HasIndex("FolderId");

                    b.HasIndex("LinkedReportId");

                    b.HasIndex("ReportId", "Name")
                        .IsUnique();

                    b.HasIndex(new[] { "IsEnabled", "Name" }, "IX_reportsection_is_enabled");

                    b.ToTable("report_section");
                });

            modelBuilder.Entity("TNO.Entities.ReportSectionChartTemplate", b =>
                {
                    b.Property<int>("ReportSectionId")
                        .HasColumnType("integer")
                        .HasColumnName("report_section_id");

                    b.Property<int>("ChartTemplateId")
                        .HasColumnType("integer")
                        .HasColumnName("chart_template_id");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("character varying(250)")
                        .HasColumnName("created_by");

                    b.Property<DateTime>("CreatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_on")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<JsonDocument>("Settings")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("jsonb")
                        .HasColumnName("settings")
                        .HasDefaultValueSql("'{}'::jsonb");

                    b.Property<int>("SortOrder")
                        .HasColumnType("integer")
                        .HasColumnName("sort_order");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("character varying(250)")
                        .HasColumnName("updated_by");

                    b.Property<DateTime>("UpdatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_on")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<long>("Version")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasColumnName("version")
                        .HasDefaultValueSql("0");

                    b.HasKey("ReportSectionId", "ChartTemplateId");

                    b.HasIndex("ChartTemplateId");

                    b.ToTable("report_section_chart_template");
                });

            modelBuilder.Entity("TNO.Entities.ReportTemplate", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("Body")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("body");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("character varying(250)")
                        .HasColumnName("created_by");

                    b.Property<DateTime>("CreatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_on")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<string>("Description")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)")
                        .HasColumnName("description")
                        .HasDefaultValueSql("''");

                    b.Property<bool>("IsEnabled")
                        .HasColumnType("boolean")
                        .HasColumnName("is_enabled");

                    b.Property<bool>("IsPublic")
                        .HasColumnType("boolean")
                        .HasColumnName("is_public");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("name");

                    b.Property<int>("ReportType")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("report_type")
                        .HasDefaultValueSql("0");

                    b.Property<JsonDocument>("Settings")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("jsonb")
                        .HasColumnName("settings")
                        .HasDefaultValueSql("'{}'::jsonb");

                    b.Property<int>("SortOrder")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(0)
                        .HasColumnName("sort_order");

                    b.Property<string>("Subject")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("subject");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("character varying(250)")
                        .HasColumnName("updated_by");

                    b.Property<DateTime>("UpdatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_on")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<long>("Version")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasColumnName("version")
                        .HasDefaultValueSql("0");

                    b.HasKey("Id");

                    b.HasIndex("Name")
                        .IsUnique();

                    b.HasIndex("IsPublic", "IsEnabled");

                    b.HasIndex(new[] { "IsEnabled", "Name" }, "IX_reporttemplate_is_enabled");

                    b.ToTable("report_template");
                });

            modelBuilder.Entity("TNO.Entities.ReportTemplateChartTemplate", b =>
                {
                    b.Property<int>("ReportTemplateId")
                        .HasColumnType("integer")
                        .HasColumnName("report_template_id");

                    b.Property<int>("ChartTemplateId")
                        .HasColumnType("integer")
                        .HasColumnName("chart_template_id");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("character varying(250)")
                        .HasColumnName("created_by");

                    b.Property<DateTime>("CreatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_on")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("character varying(250)")
                        .HasColumnName("updated_by");

                    b.Property<DateTime>("UpdatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_on")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<long>("Version")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasColumnName("version")
                        .HasDefaultValueSql("0");

                    b.HasKey("ReportTemplateId", "ChartTemplateId");

                    b.HasIndex("ChartTemplateId");

                    b.ToTable("report_template_chart_template");
                });

            modelBuilder.Entity("TNO.Entities.Schedule", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("character varying(250)")
                        .HasColumnName("created_by");

                    b.Property<DateTime>("CreatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_on")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<int>("DayOfMonth")
                        .HasColumnType("integer")
                        .HasColumnName("day_of_month");

                    b.Property<int>("DelayMS")
                        .HasColumnType("integer")
                        .HasColumnName("delay_ms");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)")
                        .HasColumnName("description");

                    b.Property<bool>("IsEnabled")
                        .HasColumnType("boolean")
                        .HasColumnName("is_enabled");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("name");

                    b.Property<bool>("Repeat")
                        .HasColumnType("boolean")
                        .HasColumnName("repeat");

                    b.Property<int?>("RequestedById")
                        .HasColumnType("integer")
                        .HasColumnName("requested_by_id");

                    b.Property<DateTime?>("RunOn")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("run_on");

                    b.Property<int>("RunOnMonths")
                        .HasColumnType("integer")
                        .HasColumnName("run_on_months");

                    b.Property<int>("RunOnWeekDays")
                        .HasColumnType("integer")
                        .HasColumnName("run_on_week_days");

                    b.Property<bool>("RunOnlyOnce")
                        .HasColumnType("boolean")
                        .HasColumnName("run_only_once");

                    b.Property<TimeSpan?>("StartAt")
                        .HasColumnType("interval")
                        .HasColumnName("start_at");

                    b.Property<TimeSpan?>("StopAt")
                        .HasColumnType("interval")
                        .HasColumnName("stop_at");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("character varying(250)")
                        .HasColumnName("updated_by");

                    b.Property<DateTime>("UpdatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_on")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<long>("Version")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasColumnName("version")
                        .HasDefaultValueSql("0");

                    b.HasKey("Id");

                    b.HasIndex("RequestedById");

                    b.HasIndex(new[] { "Name", "IsEnabled" }, "IX_schedule");

                    b.ToTable("schedule");
                });

            modelBuilder.Entity("TNO.Entities.Sentiment", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("character varying(250)")
                        .HasColumnName("created_by");

                    b.Property<DateTime>("CreatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_on")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<string>("Description")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)")
                        .HasColumnName("description")
                        .HasDefaultValueSql("''");

                    b.Property<bool>("IsEnabled")
                        .HasColumnType("boolean")
                        .HasColumnName("is_enabled");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("name");

                    b.Property<float>("Rate")
                        .HasColumnType("real")
                        .HasColumnName("rate");

                    b.Property<int>("SortOrder")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(0)
                        .HasColumnName("sort_order");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("character varying(250)")
                        .HasColumnName("updated_by");

                    b.Property<DateTime>("UpdatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_on")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<float>("Value")
                        .HasColumnType("real")
                        .HasColumnName("value");

                    b.Property<long>("Version")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasColumnName("version")
                        .HasDefaultValueSql("0");

                    b.HasKey("Id");

                    b.HasIndex("Name")
                        .IsUnique();

                    b.HasIndex(new[] { "IsEnabled", "Name" }, "IX_sentiment_is_enabled");

                    b.ToTable("sentiment");
                });

            modelBuilder.Entity("TNO.Entities.Series", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<bool>("AutoTranscribe")
                        .HasColumnType("boolean")
                        .HasColumnName("auto_transcribe");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("character varying(250)")
                        .HasColumnName("created_by");

                    b.Property<DateTime>("CreatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_on")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<string>("Description")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)")
                        .HasColumnName("description")
                        .HasDefaultValueSql("''");

                    b.Property<bool>("IsCBRASource")
                        .HasColumnType("boolean")
                        .HasColumnName("is_cbra_source");

                    b.Property<bool>("IsEnabled")
                        .HasColumnType("boolean")
                        .HasColumnName("is_enabled");

                    b.Property<bool>("IsOther")
                        .HasColumnType("boolean")
                        .HasColumnName("is_other");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("name");

                    b.Property<int>("SortOrder")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(0)
                        .HasColumnName("sort_order");

                    b.Property<int?>("SourceId")
                        .HasColumnType("integer")
                        .HasColumnName("source_id");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("character varying(250)")
                        .HasColumnName("updated_by");

                    b.Property<DateTime>("UpdatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_on")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<bool>("UseInTopics")
                        .HasColumnType("boolean")
                        .HasColumnName("use_in_topics");

                    b.Property<long>("Version")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasColumnName("version")
                        .HasDefaultValueSql("0");

                    b.HasKey("Id");

                    b.HasIndex("SourceId");

                    b.HasIndex(new[] { "IsEnabled", "Name" }, "IX_series_is_enabled");

                    b.ToTable("series");
                });

            modelBuilder.Entity("TNO.Entities.SeriesMediaTypeSearchMapping", b =>
                {
                    b.Property<int>("SeriesId")
                        .HasColumnType("integer")
                        .HasColumnName("series_id");

                    b.Property<int>("MediaTypeId")
                        .HasColumnType("integer")
                        .HasColumnName("media_type_id");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("character varying(250)")
                        .HasColumnName("created_by");

                    b.Property<DateTime>("CreatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_on")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("character varying(250)")
                        .HasColumnName("updated_by");

                    b.Property<DateTime>("UpdatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_on")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<long>("Version")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasColumnName("version")
                        .HasDefaultValueSql("0");

                    b.HasKey("SeriesId", "MediaTypeId");

                    b.HasIndex("MediaTypeId");

                    b.ToTable("series_media_type_search_mapping");
                });

            modelBuilder.Entity("TNO.Entities.Setting", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("character varying(250)")
                        .HasColumnName("created_by");

                    b.Property<DateTime>("CreatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_on")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<string>("Description")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)")
                        .HasColumnName("description")
                        .HasDefaultValueSql("''");

                    b.Property<bool>("IsEnabled")
                        .HasColumnType("boolean")
                        .HasColumnName("is_enabled");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("name");

                    b.Property<int>("SortOrder")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(0)
                        .HasColumnName("sort_order");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("character varying(250)")
                        .HasColumnName("updated_by");

                    b.Property<DateTime>("UpdatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_on")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<string>("Value")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)")
                        .HasColumnName("value")
                        .HasDefaultValueSql("''");

                    b.Property<long>("Version")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasColumnName("version")
                        .HasDefaultValueSql("0");

                    b.HasKey("Id");

                    b.HasIndex(new[] { "IsEnabled", "Name" }, "IX_setting_is_enabled");

                    b.HasIndex(new[] { "Name" }, "IX_setting_name")
                        .IsUnique();

                    b.ToTable("setting");
                });

            modelBuilder.Entity("TNO.Entities.Source", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<bool>("AutoTranscribe")
                        .HasColumnType("boolean")
                        .HasColumnName("auto_transcribe");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)")
                        .HasColumnName("code");

                    b.Property<JsonDocument>("Configuration")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("jsonb")
                        .HasColumnName("configuration")
                        .HasDefaultValueSql("'{}'::jsonb");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("character varying(250)")
                        .HasColumnName("created_by");

                    b.Property<DateTime>("CreatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_on")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<string>("Description")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)")
                        .HasColumnName("description")
                        .HasDefaultValueSql("''");

                    b.Property<bool>("DisableTranscribe")
                        .HasColumnType("boolean")
                        .HasColumnName("disable_transcribe");

                    b.Property<bool>("IsCBRASource")
                        .HasColumnType("boolean")
                        .HasColumnName("is_cbra_source");

                    b.Property<bool>("IsEnabled")
                        .HasColumnType("boolean")
                        .HasColumnName("is_enabled");

                    b.Property<int>("LicenseId")
                        .HasColumnType("integer")
                        .HasColumnName("license_id");

                    b.Property<int?>("MediaTypeId")
                        .HasColumnType("integer")
                        .HasColumnName("media_type_id");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("name");

                    b.Property<int?>("OwnerId")
                        .HasColumnType("integer")
                        .HasColumnName("owner_id");

                    b.Property<string>("ShortName")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("short_name")
                        .HasDefaultValueSql("''");

                    b.Property<int>("SortOrder")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(0)
                        .HasColumnName("sort_order");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("character varying(250)")
                        .HasColumnName("updated_by");

                    b.Property<DateTime>("UpdatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_on")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<bool>("UseInTopics")
                        .HasColumnType("boolean")
                        .HasColumnName("use_in_topics");

                    b.Property<long>("Version")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasColumnName("version")
                        .HasDefaultValueSql("0");

                    b.HasKey("Id");

                    b.HasIndex("LicenseId");

                    b.HasIndex("MediaTypeId");

                    b.HasIndex("OwnerId");

                    b.HasIndex(new[] { "Code" }, "IX_source_code")
                        .IsUnique();

                    b.HasIndex(new[] { "IsEnabled", "Name" }, "IX_source_is_enabled");

                    b.HasIndex(new[] { "Name" }, "IX_source_name")
                        .IsUnique();

                    b.ToTable("source");
                });

            modelBuilder.Entity("TNO.Entities.SourceMediaTypeSearchMapping", b =>
                {
                    b.Property<int>("SourceId")
                        .HasColumnType("integer")
                        .HasColumnName("source_id");

                    b.Property<int>("MediaTypeId")
                        .HasColumnType("integer")
                        .HasColumnName("media_type_id");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("character varying(250)")
                        .HasColumnName("created_by");

                    b.Property<DateTime>("CreatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_on")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("character varying(250)")
                        .HasColumnName("updated_by");

                    b.Property<DateTime>("UpdatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_on")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<long>("Version")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasColumnName("version")
                        .HasDefaultValueSql("0");

                    b.HasKey("SourceId", "MediaTypeId");

                    b.HasIndex("MediaTypeId");

                    b.ToTable("source_media_type_search_mapping");
                });

            modelBuilder.Entity("TNO.Entities.SourceMetric", b =>
                {
                    b.Property<int>("SourceId")
                        .HasColumnType("integer")
                        .HasColumnName("source_id");

                    b.Property<int>("MetricId")
                        .HasColumnType("integer")
                        .HasColumnName("metric_id");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("character varying(250)")
                        .HasColumnName("created_by");

                    b.Property<DateTime>("CreatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_on")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<float>("Earned")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("real")
                        .HasDefaultValue(0f)
                        .HasColumnName("earned");

                    b.Property<float>("Impression")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("real")
                        .HasDefaultValue(0f)
                        .HasColumnName("impression");

                    b.Property<float>("Reach")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("real")
                        .HasDefaultValue(0f)
                        .HasColumnName("reach");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("character varying(250)")
                        .HasColumnName("updated_by");

                    b.Property<DateTime>("UpdatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_on")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<long>("Version")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasColumnName("version")
                        .HasDefaultValueSql("0");

                    b.HasKey("SourceId", "MetricId");

                    b.HasIndex("MetricId");

                    b.ToTable("source_metric");
                });

            modelBuilder.Entity("TNO.Entities.SystemMessage", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("created_by");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_on");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("description");

                    b.Property<bool>("IsEnabled")
                        .HasColumnType("boolean")
                        .HasColumnName("is_enabled");

                    b.Property<string>("Message")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("message");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("name");

                    b.Property<int>("SortOrder")
                        .HasColumnType("integer")
                        .HasColumnName("sort_order");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("updated_by");

                    b.Property<DateTime>("UpdatedOn")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_on");

                    b.Property<long>("Version")
                        .HasColumnType("bigint")
                        .HasColumnName("version");

                    b.HasKey("Id");

                    b.ToTable("system_message");
                });

            modelBuilder.Entity("TNO.Entities.Tag", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(15)
                        .HasColumnType("character varying(15)")
                        .HasColumnName("code");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("character varying(250)")
                        .HasColumnName("created_by");

                    b.Property<DateTime>("CreatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_on")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<string>("Description")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)")
                        .HasColumnName("description")
                        .HasDefaultValueSql("''");

                    b.Property<bool>("IsEnabled")
                        .HasColumnType("boolean")
                        .HasColumnName("is_enabled");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("name");

                    b.Property<int>("SortOrder")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(0)
                        .HasColumnName("sort_order");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("character varying(250)")
                        .HasColumnName("updated_by");

                    b.Property<DateTime>("UpdatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_on")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<long>("Version")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasColumnName("version")
                        .HasDefaultValueSql("0");

                    b.HasKey("Id");

                    b.HasIndex(new[] { "Code" }, "IX_tag_code")
                        .IsUnique();

                    b.HasIndex(new[] { "IsEnabled", "Name" }, "IX_tag_is_enabled");

                    b.HasIndex(new[] { "Name" }, "IX_tag_name")
                        .IsUnique();

                    b.ToTable("tag");
                });

            modelBuilder.Entity("TNO.Entities.TimeTracking", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<long>("Id"));

                    b.Property<string>("Activity")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("activity");

                    b.Property<long>("ContentId")
                        .HasColumnType("bigint")
                        .HasColumnName("content_id");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("character varying(250)")
                        .HasColumnName("created_by");

                    b.Property<DateTime>("CreatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_on")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<float>("Effort")
                        .HasColumnType("real")
                        .HasColumnName("effort");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("character varying(250)")
                        .HasColumnName("updated_by");

                    b.Property<DateTime>("UpdatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_on")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<int>("UserId")
                        .HasColumnType("integer")
                        .HasColumnName("user_id");

                    b.Property<long>("Version")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasColumnName("version")
                        .HasDefaultValueSql("0");

                    b.HasKey("Id");

                    b.HasIndex("ContentId");

                    b.HasIndex("UserId");

                    b.ToTable("time_tracking");
                });

            modelBuilder.Entity("TNO.Entities.TonePool", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("character varying(250)")
                        .HasColumnName("created_by");

                    b.Property<DateTime>("CreatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_on")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<string>("Description")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)")
                        .HasColumnName("description")
                        .HasDefaultValueSql("''");

                    b.Property<bool>("IsEnabled")
                        .HasColumnType("boolean")
                        .HasColumnName("is_enabled");

                    b.Property<bool>("IsPublic")
                        .HasColumnType("boolean")
                        .HasColumnName("is_public");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("name");

                    b.Property<int>("OwnerId")
                        .HasColumnType("integer")
                        .HasColumnName("owner_id");

                    b.Property<int>("SortOrder")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(0)
                        .HasColumnName("sort_order");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("character varying(250)")
                        .HasColumnName("updated_by");

                    b.Property<DateTime>("UpdatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_on")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<long>("Version")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasColumnName("version")
                        .HasDefaultValueSql("0");

                    b.HasKey("Id");

                    b.HasIndex(new[] { "OwnerId", "Name" }, "IX_tone_pool_name")
                        .IsUnique();

                    b.HasIndex(new[] { "IsEnabled", "Name" }, "IX_tonepool_is_enabled");

                    b.ToTable("tone_pool");
                });

            modelBuilder.Entity("TNO.Entities.Topic", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("character varying(250)")
                        .HasColumnName("created_by");

                    b.Property<DateTime>("CreatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_on")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<string>("Description")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)")
                        .HasColumnName("description")
                        .HasDefaultValueSql("''");

                    b.Property<bool>("IsEnabled")
                        .HasColumnType("boolean")
                        .HasColumnName("is_enabled");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("name");

                    b.Property<int>("SortOrder")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(0)
                        .HasColumnName("sort_order");

                    b.Property<int>("TopicType")
                        .HasColumnType("integer")
                        .HasColumnName("topic_type");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("character varying(250)")
                        .HasColumnName("updated_by");

                    b.Property<DateTime>("UpdatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_on")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<long>("Version")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasColumnName("version")
                        .HasDefaultValueSql("0");

                    b.HasKey("Id");

                    b.HasIndex(new[] { "IsEnabled", "Name" }, "IX_topic_is_enabled");

                    b.HasIndex(new[] { "Name" }, "IX_topic_name")
                        .IsUnique();

                    b.ToTable("topic");
                });

            modelBuilder.Entity("TNO.Entities.TopicScoreRule", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<int?>("CharacterMax")
                        .HasColumnType("integer")
                        .HasColumnName("char_max");

                    b.Property<int?>("CharacterMin")
                        .HasColumnType("integer")
                        .HasColumnName("char_min");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("character varying(250)")
                        .HasColumnName("created_by");

                    b.Property<DateTime>("CreatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_on")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<bool?>("HasImage")
                        .HasColumnType("boolean")
                        .HasColumnName("has_image");

                    b.Property<string>("PageMax")
                        .HasMaxLength(5)
                        .HasColumnType("character varying(5)")
                        .HasColumnName("page_max");

                    b.Property<string>("PageMin")
                        .HasMaxLength(5)
                        .HasColumnType("character varying(5)")
                        .HasColumnName("page_min");

                    b.Property<int>("Score")
                        .HasColumnType("integer")
                        .HasColumnName("score");

                    b.Property<string>("Section")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("section");

                    b.Property<int?>("SeriesId")
                        .HasColumnType("integer")
                        .HasColumnName("series_id");

                    b.Property<int>("SortOrder")
                        .HasColumnType("integer")
                        .HasColumnName("sort_order");

                    b.Property<int>("SourceId")
                        .HasColumnType("integer")
                        .HasColumnName("source_id");

                    b.Property<TimeSpan?>("TimeMax")
                        .HasColumnType("interval")
                        .HasColumnName("time_max");

                    b.Property<TimeSpan?>("TimeMin")
                        .HasColumnType("interval")
                        .HasColumnName("time_min");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("character varying(250)")
                        .HasColumnName("updated_by");

                    b.Property<DateTime>("UpdatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_on")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<long>("Version")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasColumnName("version")
                        .HasDefaultValueSql("0");

                    b.HasKey("Id");

                    b.HasIndex("SeriesId");

                    b.HasIndex(new[] { "SourceId", "SeriesId", "Section" }, "IX_source_id_series_id_section");

                    b.ToTable("topic_score_rule");
                });

            modelBuilder.Entity("TNO.Entities.User", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<int>("AccountType")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(0)
                        .HasColumnName("account_type");

                    b.Property<string>("Code")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(10)
                        .HasColumnType("character varying(10)")
                        .HasColumnName("code")
                        .HasDefaultValueSql("''");

                    b.Property<DateTime?>("CodeCreatedOn")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("code_created_on");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("character varying(250)")
                        .HasColumnName("created_by");

                    b.Property<DateTime>("CreatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_on")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<string>("DisplayName")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("display_name")
                        .HasDefaultValueSql("''");

                    b.Property<string>("Email")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(250)
                        .HasColumnType("character varying(250)")
                        .HasColumnName("email")
                        .HasDefaultValueSql("''");

                    b.Property<bool>("EmailVerified")
                        .HasColumnType("boolean")
                        .HasColumnName("email_verified");

                    b.Property<string>("FirstName")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("first_name")
                        .HasDefaultValueSql("''");

                    b.Property<bool>("IsEnabled")
                        .HasColumnType("boolean")
                        .HasColumnName("is_enabled");

                    b.Property<bool>("IsSystemAccount")
                        .HasColumnType("boolean")
                        .HasColumnName("is_system_account");

                    b.Property<string>("Key")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("character varying(250)")
                        .HasColumnName("key");

                    b.Property<DateTime?>("LastLoginOn")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("last_login_on");

                    b.Property<string>("LastName")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("last_name")
                        .HasDefaultValueSql("''");

                    b.Property<string>("Note")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)")
                        .HasColumnName("note")
                        .HasDefaultValueSql("''");

                    b.Property<JsonDocument>("Preferences")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("jsonb")
                        .HasColumnName("preferences")
                        .HasDefaultValueSql("'{}'::jsonb");

                    b.Property<string>("PreferredEmail")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(250)
                        .HasColumnType("character varying(250)")
                        .HasColumnName("preferred_email")
                        .HasDefaultValueSql("''");

                    b.Property<string>("Roles")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)")
                        .HasColumnName("roles")
                        .HasDefaultValueSql("''");

                    b.Property<int>("Status")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(0)
                        .HasColumnName("status");

                    b.Property<int>("UniqueLogins")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("unique_logins")
                        .HasDefaultValueSql("0");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("character varying(250)")
                        .HasColumnName("updated_by");

                    b.Property<DateTime>("UpdatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_on")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<string>("Username")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("username");

                    b.Property<long>("Version")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasColumnName("version")
                        .HasDefaultValueSql("0");

                    b.HasKey("Id");

                    b.HasIndex(new[] { "Email" }, "IX_email");

                    b.HasIndex(new[] { "Key" }, "IX_key")
                        .IsUnique();

                    b.HasIndex(new[] { "LastName", "FirstName" }, "IX_last_first_name");

                    b.HasIndex(new[] { "Username" }, "IX_username")
                        .IsUnique();

                    b.ToTable("user");
                });

            modelBuilder.Entity("TNO.Entities.UserAVOverview", b =>
                {
                    b.Property<int>("UserId")
                        .HasColumnType("integer")
                        .HasColumnName("user_id");

                    b.Property<int>("TemplateType")
                        .HasColumnType("integer")
                        .HasColumnName("av_overview_template_type");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("character varying(250)")
                        .HasColumnName("created_by");

                    b.Property<DateTime>("CreatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_on")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<bool>("IsSubscribed")
                        .HasColumnType("boolean")
                        .HasColumnName("is_subscribed");

                    b.Property<int>("SendTo")
                        .HasColumnType("integer")
                        .HasColumnName("send_to");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("character varying(250)")
                        .HasColumnName("updated_by");

                    b.Property<DateTime>("UpdatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_on")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<long>("Version")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasColumnName("version")
                        .HasDefaultValueSql("0");

                    b.HasKey("UserId", "TemplateType");

                    b.HasIndex("TemplateType");

                    b.ToTable("user_av_overview");
                });

            modelBuilder.Entity("TNO.Entities.UserAVOverviewInstance", b =>
                {
                    b.Property<int>("UserId")
                        .HasColumnType("integer")
                        .HasColumnName("user_id");

                    b.Property<long>("InstanceId")
                        .HasColumnType("bigint")
                        .HasColumnName("report_instance_id");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("character varying(250)")
                        .HasColumnName("created_by");

                    b.Property<DateTime>("CreatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_on")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<JsonDocument>("Response")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("jsonb")
                        .HasColumnName("response")
                        .HasDefaultValueSql("'{}'::jsonb");

                    b.Property<DateTime?>("SentOn")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("sent_on");

                    b.Property<int>("Status")
                        .HasColumnType("integer")
                        .HasColumnName("status");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("character varying(250)")
                        .HasColumnName("updated_by");

                    b.Property<DateTime>("UpdatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_on")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<long>("Version")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasColumnName("version")
                        .HasDefaultValueSql("0");

                    b.HasKey("UserId", "InstanceId");

                    b.HasIndex("InstanceId");

                    b.HasIndex(new[] { "SentOn", "Status" }, "IX_user_av_overview_instance");

                    b.ToTable("user_av_overview_instance");
                });

            modelBuilder.Entity("TNO.Entities.UserColleague", b =>
                {
                    b.Property<int>("UserId")
                        .HasColumnType("integer")
                        .HasColumnName("user_id");

                    b.Property<int>("ColleagueId")
                        .HasColumnType("integer")
                        .HasColumnName("colleague_id");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("character varying(250)")
                        .HasColumnName("created_by");

                    b.Property<DateTime>("CreatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_on")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("character varying(250)")
                        .HasColumnName("updated_by");

                    b.Property<DateTime>("UpdatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_on")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<long>("Version")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasColumnName("version")
                        .HasDefaultValueSql("0");

                    b.HasKey("UserId", "ColleagueId");

                    b.HasIndex("ColleagueId");

                    b.ToTable("user_colleague");
                });

            modelBuilder.Entity("TNO.Entities.UserContentNotification", b =>
                {
                    b.Property<int>("UserId")
                        .HasColumnType("integer")
                        .HasColumnName("user_id");

                    b.Property<long>("ContentId")
                        .HasColumnType("bigint")
                        .HasColumnName("content_id");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("character varying(250)")
                        .HasColumnName("created_by");

                    b.Property<DateTime>("CreatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_on")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<bool>("IsSubscribed")
                        .HasColumnType("boolean")
                        .HasColumnName("is_subscribed");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("character varying(250)")
                        .HasColumnName("updated_by");

                    b.Property<DateTime>("UpdatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_on")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<long>("Version")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasColumnName("version")
                        .HasDefaultValueSql("0");

                    b.HasKey("UserId", "ContentId");

                    b.HasIndex("ContentId");

                    b.ToTable("user_content_notification");
                });

            modelBuilder.Entity("TNO.Entities.UserDistribution", b =>
                {
                    b.Property<int>("UserId")
                        .HasColumnType("integer")
                        .HasColumnName("user_id");

                    b.Property<int>("LinkedUserId")
                        .HasColumnType("integer")
                        .HasColumnName("linked_user_id");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("character varying(250)")
                        .HasColumnName("created_by");

                    b.Property<DateTime>("CreatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_on")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("character varying(250)")
                        .HasColumnName("updated_by");

                    b.Property<DateTime>("UpdatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_on")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<long>("Version")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasColumnName("version")
                        .HasDefaultValueSql("0");

                    b.HasKey("UserId", "LinkedUserId");

                    b.HasIndex("LinkedUserId");

                    b.ToTable("user_distribution");
                });

            modelBuilder.Entity("TNO.Entities.UserMediaType", b =>
                {
                    b.Property<int>("UserId")
                        .HasColumnType("integer")
                        .HasColumnName("user_id");

                    b.Property<int>("MediaTypeId")
                        .HasColumnType("integer")
                        .HasColumnName("media_type_id");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("character varying(250)")
                        .HasColumnName("created_by");

                    b.Property<DateTime>("CreatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_on")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("character varying(250)")
                        .HasColumnName("updated_by");

                    b.Property<DateTime>("UpdatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_on")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<long>("Version")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasColumnName("version")
                        .HasDefaultValueSql("0");

                    b.HasKey("UserId", "MediaTypeId");

                    b.HasIndex("MediaTypeId");

                    b.ToTable("user_media_type");
                });

            modelBuilder.Entity("TNO.Entities.UserNotification", b =>
                {
                    b.Property<int>("UserId")
                        .HasColumnType("integer")
                        .HasColumnName("user_id");

                    b.Property<int>("NotificationId")
                        .HasColumnType("integer")
                        .HasColumnName("notification_id");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("character varying(250)")
                        .HasColumnName("created_by");

                    b.Property<DateTime>("CreatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_on")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<bool>("IsSubscribed")
                        .HasColumnType("boolean")
                        .HasColumnName("is_subscribed");

                    b.Property<int?>("Resend")
                        .HasColumnType("integer")
                        .HasColumnName("resend");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("character varying(250)")
                        .HasColumnName("updated_by");

                    b.Property<DateTime>("UpdatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_on")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<long>("Version")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasColumnName("version")
                        .HasDefaultValueSql("0");

                    b.HasKey("UserId", "NotificationId");

                    b.HasIndex("NotificationId");

                    b.ToTable("user_notification");
                });

            modelBuilder.Entity("TNO.Entities.UserOrganization", b =>
                {
                    b.Property<int>("UserId")
                        .HasColumnType("integer")
                        .HasColumnName("user_id");

                    b.Property<int>("OrganizationId")
                        .HasColumnType("integer")
                        .HasColumnName("organization_id");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("character varying(250)")
                        .HasColumnName("created_by");

                    b.Property<DateTime>("CreatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_on")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("character varying(250)")
                        .HasColumnName("updated_by");

                    b.Property<DateTime>("UpdatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_on")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<long>("Version")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasColumnName("version")
                        .HasDefaultValueSql("0");

                    b.HasKey("UserId", "OrganizationId");

                    b.HasIndex("OrganizationId");

                    b.ToTable("user_organization");
                });

            modelBuilder.Entity("TNO.Entities.UserProduct", b =>
                {
                    b.Property<int>("UserId")
                        .HasColumnType("integer")
                        .HasColumnName("user_id");

                    b.Property<int>("ProductId")
                        .HasColumnType("integer")
                        .HasColumnName("product_id");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("character varying(250)")
                        .HasColumnName("created_by");

                    b.Property<DateTime>("CreatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_on")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<int>("Status")
                        .HasColumnType("integer")
                        .HasColumnName("status");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("character varying(250)")
                        .HasColumnName("updated_by");

                    b.Property<DateTime>("UpdatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_on")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<long>("Version")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasColumnName("version")
                        .HasDefaultValueSql("0");

                    b.HasKey("UserId", "ProductId");

                    b.HasIndex("ProductId");

                    b.ToTable("user_product");
                });

            modelBuilder.Entity("TNO.Entities.UserReport", b =>
                {
                    b.Property<int>("UserId")
                        .HasColumnType("integer")
                        .HasColumnName("user_id");

                    b.Property<int>("ReportId")
                        .HasColumnType("integer")
                        .HasColumnName("report_id");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("character varying(250)")
                        .HasColumnName("created_by");

                    b.Property<DateTime>("CreatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_on")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<int>("Format")
                        .HasColumnType("integer")
                        .HasColumnName("format");

                    b.Property<bool>("IsSubscribed")
                        .HasColumnType("boolean")
                        .HasColumnName("is_subscribed");

                    b.Property<int>("SendTo")
                        .HasColumnType("integer")
                        .HasColumnName("send_to");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("character varying(250)")
                        .HasColumnName("updated_by");

                    b.Property<DateTime>("UpdatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_on")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<long>("Version")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasColumnName("version")
                        .HasDefaultValueSql("0");

                    b.HasKey("UserId", "ReportId");

                    b.HasIndex("ReportId");

                    b.ToTable("user_report");
                });

            modelBuilder.Entity("TNO.Entities.UserReportInstance", b =>
                {
                    b.Property<int>("UserId")
                        .HasColumnType("integer")
                        .HasColumnName("user_id");

                    b.Property<long>("InstanceId")
                        .HasColumnType("bigint")
                        .HasColumnName("report_instance_id");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("character varying(250)")
                        .HasColumnName("created_by");

                    b.Property<DateTime>("CreatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_on")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<JsonDocument>("LinkResponse")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("jsonb")
                        .HasColumnName("link_response")
                        .HasDefaultValueSql("'{}'::jsonb");

                    b.Property<DateTime?>("LinkSentOn")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("link_sent_on");

                    b.Property<int>("LinkStatus")
                        .HasColumnType("integer")
                        .HasColumnName("link_status");

                    b.Property<JsonDocument>("TextResponse")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("jsonb")
                        .HasColumnName("text_response")
                        .HasDefaultValueSql("'{}'::jsonb");

                    b.Property<DateTime?>("TextSentOn")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("text_sent_on");

                    b.Property<int>("TextStatus")
                        .HasColumnType("integer")
                        .HasColumnName("text_status");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("character varying(250)")
                        .HasColumnName("updated_by");

                    b.Property<DateTime>("UpdatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_on")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<long>("Version")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasColumnName("version")
                        .HasDefaultValueSql("0");

                    b.HasKey("UserId", "InstanceId");

                    b.HasIndex("InstanceId");

                    b.HasIndex(new[] { "LinkSentOn", "LinkStatus" }, "IX_user_report_instance_link");

                    b.HasIndex(new[] { "TextSentOn", "TextStatus" }, "IX_user_report_instance_text");

                    b.ToTable("user_report_instance");
                });

            modelBuilder.Entity("TNO.Entities.UserSource", b =>
                {
                    b.Property<int>("UserId")
                        .HasColumnType("integer")
                        .HasColumnName("user_id");

                    b.Property<int>("SourceId")
                        .HasColumnType("integer")
                        .HasColumnName("source_id");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("character varying(250)")
                        .HasColumnName("created_by");

                    b.Property<DateTime>("CreatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_on")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("character varying(250)")
                        .HasColumnName("updated_by");

                    b.Property<DateTime>("UpdatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_on")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<long>("Version")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasColumnName("version")
                        .HasDefaultValueSql("0");

                    b.HasKey("UserId", "SourceId");

                    b.HasIndex("SourceId");

                    b.ToTable("user_source");
                });

            modelBuilder.Entity("TNO.Entities.UserUpdateHistory", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<long>("Id"));

                    b.Property<int>("ChangeType")
                        .HasColumnType("integer")
                        .HasColumnName("change_type");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("character varying(250)")
                        .HasColumnName("created_by");

                    b.Property<DateTime>("CreatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_on")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<DateTime>("DateOfChange")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("date_of_change");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("character varying(250)")
                        .HasColumnName("updated_by");

                    b.Property<DateTime>("UpdatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_on")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<int>("UserId")
                        .HasColumnType("integer")
                        .HasColumnName("user_id");

                    b.Property<string>("Value")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("value");

                    b.Property<long>("Version")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasColumnName("version")
                        .HasDefaultValueSql("0");

                    b.HasKey("Id");

                    b.HasIndex("UserId");

                    b.ToTable("user_update_history");
                });

            modelBuilder.Entity("TNO.Entities.WorkOrder", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<long>("Id"));

                    b.Property<int?>("AssignedId")
                        .HasColumnType("integer")
                        .HasColumnName("assigned_id");

                    b.Property<JsonDocument>("Configuration")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("jsonb")
                        .HasColumnName("configuration")
                        .HasDefaultValueSql("'{}'::jsonb");

                    b.Property<long?>("ContentId")
                        .HasColumnType("bigint")
                        .HasColumnName("content_id");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("character varying(250)")
                        .HasColumnName("created_by");

                    b.Property<DateTime>("CreatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_on")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)")
                        .HasColumnName("description");

                    b.Property<string>("Note")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("note");

                    b.Property<int?>("RequestorId")
                        .HasColumnType("integer")
                        .HasColumnName("requestor_id");

                    b.Property<int>("Status")
                        .HasColumnType("integer")
                        .HasColumnName("status");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("character varying(250)")
                        .HasColumnName("updated_by");

                    b.Property<DateTime>("UpdatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_on")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<long>("Version")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasColumnName("version")
                        .HasDefaultValueSql("0");

                    b.Property<int>("WorkType")
                        .HasColumnType("integer")
                        .HasColumnName("work_type");

                    b.HasKey("Id");

                    b.HasIndex("AssignedId");

                    b.HasIndex("ContentId");

                    b.HasIndex("RequestorId");

                    b.HasIndex(new[] { "WorkType", "Status", "CreatedOn", "RequestorId", "AssignedId" }, "IX_work_order");

                    b.ToTable("work_order");
                });

            modelBuilder.Entity("TNO.Entities.AVOverviewInstance", b =>
                {
                    b.HasOne("TNO.Entities.AVOverviewTemplate", "Template")
                        .WithMany("Instances")
                        .HasForeignKey("TemplateType")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Template");
                });

            modelBuilder.Entity("TNO.Entities.AVOverviewSection", b =>
                {
                    b.HasOne("TNO.Entities.AVOverviewInstance", "Instance")
                        .WithMany("Sections")
                        .HasForeignKey("InstanceId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("TNO.Entities.Series", "Series")
                        .WithMany()
                        .HasForeignKey("SeriesId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("TNO.Entities.Source", "Source")
                        .WithMany()
                        .HasForeignKey("SourceId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.Navigation("Instance");

                    b.Navigation("Series");

                    b.Navigation("Source");
                });

            modelBuilder.Entity("TNO.Entities.AVOverviewSectionItem", b =>
                {
                    b.HasOne("TNO.Entities.Content", "Content")
                        .WithMany()
                        .HasForeignKey("ContentId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("TNO.Entities.AVOverviewSection", "Section")
                        .WithMany("Items")
                        .HasForeignKey("SectionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Content");

                    b.Navigation("Section");
                });

            modelBuilder.Entity("TNO.Entities.AVOverviewTemplate", b =>
                {
                    b.HasOne("TNO.Entities.ReportTemplate", "Template")
                        .WithMany()
                        .HasForeignKey("TemplateId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Template");
                });

            modelBuilder.Entity("TNO.Entities.AVOverviewTemplateSection", b =>
                {
                    b.HasOne("TNO.Entities.Series", "Series")
                        .WithMany()
                        .HasForeignKey("SeriesId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("TNO.Entities.Source", "Source")
                        .WithMany()
                        .HasForeignKey("SourceId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("TNO.Entities.AVOverviewTemplate", "Template")
                        .WithMany("Sections")
                        .HasForeignKey("TemplateType")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Series");

                    b.Navigation("Source");

                    b.Navigation("Template");
                });

            modelBuilder.Entity("TNO.Entities.AVOverviewTemplateSectionItem", b =>
                {
                    b.HasOne("TNO.Entities.AVOverviewTemplateSection", "Section")
                        .WithMany("Items")
                        .HasForeignKey("SectionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Section");
                });

            modelBuilder.Entity("TNO.Entities.Content", b =>
                {
                    b.HasOne("TNO.Entities.Contributor", "Contributor")
                        .WithMany("Contents")
                        .HasForeignKey("ContributorId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("TNO.Entities.IngestType", null)
                        .WithMany("Contents")
                        .HasForeignKey("IngestTypeId");

                    b.HasOne("TNO.Entities.License", "License")
                        .WithMany("Contents")
                        .HasForeignKey("LicenseId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("TNO.Entities.MediaType", "MediaType")
                        .WithMany("Contents")
                        .HasForeignKey("MediaTypeId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("TNO.Entities.User", "Owner")
                        .WithMany("Contents")
                        .HasForeignKey("OwnerId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.HasOne("TNO.Entities.Series", "Series")
                        .WithMany("Contents")
                        .HasForeignKey("SeriesId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("TNO.Entities.Source", "Source")
                        .WithMany("Contents")
                        .HasForeignKey("SourceId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.Navigation("Contributor");

                    b.Navigation("License");

                    b.Navigation("MediaType");

                    b.Navigation("Owner");

                    b.Navigation("Series");

                    b.Navigation("Source");
                });

            modelBuilder.Entity("TNO.Entities.ContentAction", b =>
                {
                    b.HasOne("TNO.Entities.Action", "Action")
                        .WithMany("ContentsManyToMany")
                        .HasForeignKey("ActionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("TNO.Entities.Content", "Content")
                        .WithMany("ActionsManyToMany")
                        .HasForeignKey("ContentId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Action");

                    b.Navigation("Content");
                });

            modelBuilder.Entity("TNO.Entities.ContentLabel", b =>
                {
                    b.HasOne("TNO.Entities.Content", "Content")
                        .WithMany("Labels")
                        .HasForeignKey("ContentId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Content");
                });

            modelBuilder.Entity("TNO.Entities.ContentLink", b =>
                {
                    b.HasOne("TNO.Entities.Content", "Content")
                        .WithMany("Links")
                        .HasForeignKey("ContentId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("TNO.Entities.Content", "Link")
                        .WithMany()
                        .HasForeignKey("LinkId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Content");

                    b.Navigation("Link");
                });

            modelBuilder.Entity("TNO.Entities.ContentLog", b =>
                {
                    b.HasOne("TNO.Entities.Content", "Content")
                        .WithMany("Logs")
                        .HasForeignKey("ContentId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Content");
                });

            modelBuilder.Entity("TNO.Entities.ContentTag", b =>
                {
                    b.HasOne("TNO.Entities.Content", "Content")
                        .WithMany("TagsManyToMany")
                        .HasForeignKey("ContentId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("TNO.Entities.Tag", "Tag")
                        .WithMany("ContentsManyToMany")
                        .HasForeignKey("TagId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Content");

                    b.Navigation("Tag");
                });

            modelBuilder.Entity("TNO.Entities.ContentTonePool", b =>
                {
                    b.HasOne("TNO.Entities.Content", "Content")
                        .WithMany("TonePoolsManyToMany")
                        .HasForeignKey("ContentId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("TNO.Entities.TonePool", "TonePool")
                        .WithMany("ContentsManyToMany")
                        .HasForeignKey("TonePoolId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Content");

                    b.Navigation("TonePool");
                });

            modelBuilder.Entity("TNO.Entities.ContentTopic", b =>
                {
                    b.HasOne("TNO.Entities.Content", "Content")
                        .WithMany("TopicsManyToMany")
                        .HasForeignKey("ContentId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("TNO.Entities.Topic", "Topic")
                        .WithMany("ContentsManyToMany")
                        .HasForeignKey("TopicId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Content");

                    b.Navigation("Topic");
                });

            modelBuilder.Entity("TNO.Entities.ContentTypeAction", b =>
                {
                    b.HasOne("TNO.Entities.Action", "Action")
                        .WithMany("ContentTypes")
                        .HasForeignKey("ActionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Action");
                });

            modelBuilder.Entity("TNO.Entities.Contributor", b =>
                {
                    b.HasOne("TNO.Entities.Source", "Source")
                        .WithMany("Contributors")
                        .HasForeignKey("SourceId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.Navigation("Source");
                });

            modelBuilder.Entity("TNO.Entities.DataLocation", b =>
                {
                    b.HasOne("TNO.Entities.Connection", "Connection")
                        .WithMany("DataLocations")
                        .HasForeignKey("ConnectionId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.Navigation("Connection");
                });

            modelBuilder.Entity("TNO.Entities.EarnedMedia", b =>
                {
                    b.HasOne("TNO.Entities.Source", "Source")
                        .WithMany("EarnedMedia")
                        .HasForeignKey("SourceId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Source");
                });

            modelBuilder.Entity("TNO.Entities.EventSchedule", b =>
                {
                    b.HasOne("TNO.Entities.Folder", "Folder")
                        .WithMany("Events")
                        .HasForeignKey("FolderId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("TNO.Entities.Notification", "Notification")
                        .WithMany("Schedules")
                        .HasForeignKey("NotificationId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("TNO.Entities.Report", "Report")
                        .WithMany("Events")
                        .HasForeignKey("ReportId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("TNO.Entities.Schedule", "Schedule")
                        .WithMany("Events")
                        .HasForeignKey("ScheduleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Folder");

                    b.Navigation("Notification");

                    b.Navigation("Report");

                    b.Navigation("Schedule");
                });

            modelBuilder.Entity("TNO.Entities.FileReference", b =>
                {
                    b.HasOne("TNO.Entities.Content", "Content")
                        .WithMany("FileReferences")
                        .HasForeignKey("ContentId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Content");
                });

            modelBuilder.Entity("TNO.Entities.Filter", b =>
                {
                    b.HasOne("TNO.Entities.User", "Owner")
                        .WithMany("Filters")
                        .HasForeignKey("OwnerId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.Navigation("Owner");
                });

            modelBuilder.Entity("TNO.Entities.Folder", b =>
                {
                    b.HasOne("TNO.Entities.Filter", "Filter")
                        .WithMany("Folders")
                        .HasForeignKey("FilterId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("TNO.Entities.User", "Owner")
                        .WithMany("Folders")
                        .HasForeignKey("OwnerId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.Navigation("Filter");

                    b.Navigation("Owner");
                });

            modelBuilder.Entity("TNO.Entities.FolderContent", b =>
                {
                    b.HasOne("TNO.Entities.Content", "Content")
                        .WithMany("FoldersManyToMany")
                        .HasForeignKey("ContentId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("TNO.Entities.Folder", "Folder")
                        .WithMany("ContentManyToMany")
                        .HasForeignKey("FolderId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Content");

                    b.Navigation("Folder");
                });

            modelBuilder.Entity("TNO.Entities.Ingest", b =>
                {
                    b.HasOne("TNO.Entities.Connection", "DestinationConnection")
                        .WithMany("DestinationIngests")
                        .HasForeignKey("DestinationConnectionId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("TNO.Entities.IngestType", "IngestType")
                        .WithMany("Ingests")
                        .HasForeignKey("IngestTypeId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("TNO.Entities.MediaType", "MediaType")
                        .WithMany("Ingests")
                        .HasForeignKey("MediaTypeId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("TNO.Entities.Connection", "SourceConnection")
                        .WithMany("SourceIngests")
                        .HasForeignKey("SourceConnectionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("TNO.Entities.Source", "Source")
                        .WithMany("Ingests")
                        .HasForeignKey("SourceId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("DestinationConnection");

                    b.Navigation("IngestType");

                    b.Navigation("MediaType");

                    b.Navigation("Source");

                    b.Navigation("SourceConnection");
                });

            modelBuilder.Entity("TNO.Entities.IngestDataLocation", b =>
                {
                    b.HasOne("TNO.Entities.DataLocation", "DataLocation")
                        .WithMany("IngestsManyToMany")
                        .HasForeignKey("DataLocationId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("TNO.Entities.Ingest", "Ingest")
                        .WithMany("DataLocationsManyToMany")
                        .HasForeignKey("IngestId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("DataLocation");

                    b.Navigation("Ingest");
                });

            modelBuilder.Entity("TNO.Entities.IngestSchedule", b =>
                {
                    b.HasOne("TNO.Entities.Ingest", "Ingest")
                        .WithMany("SchedulesManyToMany")
                        .HasForeignKey("IngestId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("TNO.Entities.Schedule", "Schedule")
                        .WithMany("IngestsManyToMany")
                        .HasForeignKey("ScheduleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Ingest");

                    b.Navigation("Schedule");
                });

            modelBuilder.Entity("TNO.Entities.IngestState", b =>
                {
                    b.HasOne("TNO.Entities.Ingest", "Ingest")
                        .WithOne("State")
                        .HasForeignKey("TNO.Entities.IngestState", "IngestId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Ingest");
                });

            modelBuilder.Entity("TNO.Entities.MediaAnalytics", b =>
                {
                    b.HasOne("TNO.Entities.MediaType", "MediaType")
                        .WithMany()
                        .HasForeignKey("MediaTypeId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("TNO.Entities.Source", "Source")
                        .WithMany()
                        .HasForeignKey("SourceId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("MediaType");

                    b.Navigation("Source");
                });

            modelBuilder.Entity("TNO.Entities.Minister", b =>
                {
                    b.HasOne("TNO.Entities.Organization", "Organization")
                        .WithMany("Ministers")
                        .HasForeignKey("OrganizationId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.Navigation("Organization");
                });

            modelBuilder.Entity("TNO.Entities.Notification", b =>
                {
                    b.HasOne("TNO.Entities.User", "Owner")
                        .WithMany("Notifications")
                        .HasForeignKey("OwnerId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("TNO.Entities.NotificationTemplate", "Template")
                        .WithMany("Notifications")
                        .HasForeignKey("TemplateId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Owner");

                    b.Navigation("Template");
                });

            modelBuilder.Entity("TNO.Entities.NotificationInstance", b =>
                {
                    b.HasOne("TNO.Entities.Content", "Content")
                        .WithMany("NotificationsManyToMany")
                        .HasForeignKey("ContentId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("TNO.Entities.Notification", "Notification")
                        .WithMany("Instances")
                        .HasForeignKey("NotificationId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Content");

                    b.Navigation("Notification");
                });

            modelBuilder.Entity("TNO.Entities.Organization", b =>
                {
                    b.HasOne("TNO.Entities.Organization", "Parent")
                        .WithMany("Children")
                        .HasForeignKey("ParentId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.Navigation("Parent");
                });

            modelBuilder.Entity("TNO.Entities.Quote", b =>
                {
                    b.HasOne("TNO.Entities.Content", "Content")
                        .WithMany("Quotes")
                        .HasForeignKey("ContentId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Content");
                });

            modelBuilder.Entity("TNO.Entities.Report", b =>
                {
                    b.HasOne("TNO.Entities.User", "Owner")
                        .WithMany("Reports")
                        .HasForeignKey("OwnerId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("TNO.Entities.ReportTemplate", "Template")
                        .WithMany("Reports")
                        .HasForeignKey("TemplateId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Owner");

                    b.Navigation("Template");
                });

            modelBuilder.Entity("TNO.Entities.ReportInstance", b =>
                {
                    b.HasOne("TNO.Entities.User", "Owner")
                        .WithMany("ReportInstances")
                        .HasForeignKey("OwnerId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("TNO.Entities.Report", "Report")
                        .WithMany("Instances")
                        .HasForeignKey("ReportId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Owner");

                    b.Navigation("Report");
                });

            modelBuilder.Entity("TNO.Entities.ReportInstanceContent", b =>
                {
                    b.HasOne("TNO.Entities.Content", "Content")
                        .WithMany("ReportsManyToMany")
                        .HasForeignKey("ContentId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("TNO.Entities.ReportInstance", "Instance")
                        .WithMany("ContentManyToMany")
                        .HasForeignKey("InstanceId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Content");

                    b.Navigation("Instance");
                });

            modelBuilder.Entity("TNO.Entities.ReportSection", b =>
                {
                    b.HasOne("TNO.Entities.Filter", "Filter")
                        .WithMany("ReportSections")
                        .HasForeignKey("FilterId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("TNO.Entities.Folder", "Folder")
                        .WithMany("ReportSections")
                        .HasForeignKey("FolderId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("TNO.Entities.Report", "LinkedReport")
                        .WithMany()
                        .HasForeignKey("LinkedReportId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("TNO.Entities.Report", "Report")
                        .WithMany("Sections")
                        .HasForeignKey("ReportId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Filter");

                    b.Navigation("Folder");

                    b.Navigation("LinkedReport");

                    b.Navigation("Report");
                });

            modelBuilder.Entity("TNO.Entities.ReportSectionChartTemplate", b =>
                {
                    b.HasOne("TNO.Entities.ChartTemplate", "ChartTemplate")
                        .WithMany("ReportSectionsManyToMany")
                        .HasForeignKey("ChartTemplateId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("TNO.Entities.ReportSection", "ReportSection")
                        .WithMany("ChartTemplatesManyToMany")
                        .HasForeignKey("ReportSectionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("ChartTemplate");

                    b.Navigation("ReportSection");
                });

            modelBuilder.Entity("TNO.Entities.ReportTemplateChartTemplate", b =>
                {
                    b.HasOne("TNO.Entities.ChartTemplate", "ChartTemplate")
                        .WithMany("ReportTemplatesManyToMany")
                        .HasForeignKey("ChartTemplateId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("TNO.Entities.ReportTemplate", "ReportTemplate")
                        .WithMany("ChartTemplatesManyToMany")
                        .HasForeignKey("ReportTemplateId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("ChartTemplate");

                    b.Navigation("ReportTemplate");
                });

            modelBuilder.Entity("TNO.Entities.Schedule", b =>
                {
                    b.HasOne("TNO.Entities.User", "RequestedBy")
                        .WithMany()
                        .HasForeignKey("RequestedById")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.Navigation("RequestedBy");
                });

            modelBuilder.Entity("TNO.Entities.Series", b =>
                {
                    b.HasOne("TNO.Entities.Source", "Source")
                        .WithMany("Series")
                        .HasForeignKey("SourceId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.Navigation("Source");
                });

            modelBuilder.Entity("TNO.Entities.SeriesMediaTypeSearchMapping", b =>
                {
                    b.HasOne("TNO.Entities.MediaType", "MediaType")
                        .WithMany("SeriesSearchMappingsManyToMany")
                        .HasForeignKey("MediaTypeId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("TNO.Entities.Series", "Series")
                        .WithMany("MediaTypeSearchMappingsManyToMany")
                        .HasForeignKey("SeriesId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("MediaType");

                    b.Navigation("Series");
                });

            modelBuilder.Entity("TNO.Entities.Source", b =>
                {
                    b.HasOne("TNO.Entities.License", "License")
                        .WithMany("Sources")
                        .HasForeignKey("LicenseId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("TNO.Entities.MediaType", "MediaType")
                        .WithMany("Sources")
                        .HasForeignKey("MediaTypeId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("TNO.Entities.User", "Owner")
                        .WithMany()
                        .HasForeignKey("OwnerId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.Navigation("License");

                    b.Navigation("MediaType");

                    b.Navigation("Owner");
                });

            modelBuilder.Entity("TNO.Entities.SourceMediaTypeSearchMapping", b =>
                {
                    b.HasOne("TNO.Entities.MediaType", "MediaType")
                        .WithMany("SourceSearchMappingsManyToMany")
                        .HasForeignKey("MediaTypeId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("TNO.Entities.Source", "Source")
                        .WithMany("MediaTypeSearchMappingsManyToMany")
                        .HasForeignKey("SourceId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("MediaType");

                    b.Navigation("Source");
                });

            modelBuilder.Entity("TNO.Entities.SourceMetric", b =>
                {
                    b.HasOne("TNO.Entities.Metric", "Metric")
                        .WithMany("SourcesManyToMany")
                        .HasForeignKey("MetricId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("TNO.Entities.Source", "Source")
                        .WithMany("MetricsManyToMany")
                        .HasForeignKey("SourceId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Metric");

                    b.Navigation("Source");
                });

            modelBuilder.Entity("TNO.Entities.TimeTracking", b =>
                {
                    b.HasOne("TNO.Entities.Content", "Content")
                        .WithMany("TimeTrackings")
                        .HasForeignKey("ContentId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("TNO.Entities.User", "User")
                        .WithMany("TimeTrackings")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Content");

                    b.Navigation("User");
                });

            modelBuilder.Entity("TNO.Entities.TonePool", b =>
                {
                    b.HasOne("TNO.Entities.User", "Owner")
                        .WithMany("TonePools")
                        .HasForeignKey("OwnerId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Owner");
                });

            modelBuilder.Entity("TNO.Entities.TopicScoreRule", b =>
                {
                    b.HasOne("TNO.Entities.Series", "Series")
                        .WithMany("ScoreRules")
                        .HasForeignKey("SeriesId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("TNO.Entities.Source", "Source")
                        .WithMany("ScoreRules")
                        .HasForeignKey("SourceId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Series");

                    b.Navigation("Source");
                });

            modelBuilder.Entity("TNO.Entities.UserAVOverview", b =>
                {
                    b.HasOne("TNO.Entities.AVOverviewTemplate", "Template")
                        .WithMany("SubscribersManyToMany")
                        .HasForeignKey("TemplateType")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("TNO.Entities.User", "User")
                        .WithMany("AVOverviewSubscriptionsManyToMany")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Template");

                    b.Navigation("User");
                });

            modelBuilder.Entity("TNO.Entities.UserAVOverviewInstance", b =>
                {
                    b.HasOne("TNO.Entities.AVOverviewInstance", "Instance")
                        .WithMany("UserInstances")
                        .HasForeignKey("InstanceId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("TNO.Entities.User", "User")
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Instance");

                    b.Navigation("User");
                });

            modelBuilder.Entity("TNO.Entities.UserColleague", b =>
                {
                    b.HasOne("TNO.Entities.User", "Colleague")
                        .WithMany()
                        .HasForeignKey("ColleagueId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("TNO.Entities.User", "User")
                        .WithMany("ColleaguesManyToMany")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Colleague");

                    b.Navigation("User");
                });

            modelBuilder.Entity("TNO.Entities.UserContentNotification", b =>
                {
                    b.HasOne("TNO.Entities.Content", "Content")
                        .WithMany("UserNotifications")
                        .HasForeignKey("ContentId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("TNO.Entities.User", "User")
                        .WithMany("ContentNotifications")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Content");

                    b.Navigation("User");
                });

            modelBuilder.Entity("TNO.Entities.UserDistribution", b =>
                {
                    b.HasOne("TNO.Entities.User", "LinkedUser")
                        .WithMany()
                        .HasForeignKey("LinkedUserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("TNO.Entities.User", "User")
                        .WithMany("Distribution")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("LinkedUser");

                    b.Navigation("User");
                });

            modelBuilder.Entity("TNO.Entities.UserMediaType", b =>
                {
                    b.HasOne("TNO.Entities.MediaType", "MediaType")
                        .WithMany("UsersManyToMany")
                        .HasForeignKey("MediaTypeId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("TNO.Entities.User", "User")
                        .WithMany("MediaTypesManyToMany")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("MediaType");

                    b.Navigation("User");
                });

            modelBuilder.Entity("TNO.Entities.UserNotification", b =>
                {
                    b.HasOne("TNO.Entities.Notification", "Notification")
                        .WithMany("SubscribersManyToMany")
                        .HasForeignKey("NotificationId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("TNO.Entities.User", "User")
                        .WithMany("NotificationSubscriptionsManyToMany")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Notification");

                    b.Navigation("User");
                });

            modelBuilder.Entity("TNO.Entities.UserOrganization", b =>
                {
                    b.HasOne("TNO.Entities.Organization", "Organization")
                        .WithMany("UsersManyToMany")
                        .HasForeignKey("OrganizationId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("TNO.Entities.User", "User")
                        .WithMany("OrganizationsManyToMany")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Organization");

                    b.Navigation("User");
                });

            modelBuilder.Entity("TNO.Entities.UserProduct", b =>
                {
                    b.HasOne("TNO.Entities.Product", "Product")
                        .WithMany("SubscribersManyToMany")
                        .HasForeignKey("ProductId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("TNO.Entities.User", "User")
                        .WithMany("ProductSubscriptionsManyToMany")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Product");

                    b.Navigation("User");
                });

            modelBuilder.Entity("TNO.Entities.UserReport", b =>
                {
                    b.HasOne("TNO.Entities.Report", "Report")
                        .WithMany("SubscribersManyToMany")
                        .HasForeignKey("ReportId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("TNO.Entities.User", "User")
                        .WithMany("ReportSubscriptionsManyToMany")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Report");

                    b.Navigation("User");
                });

            modelBuilder.Entity("TNO.Entities.UserReportInstance", b =>
                {
                    b.HasOne("TNO.Entities.ReportInstance", "Instance")
                        .WithMany("UserInstances")
                        .HasForeignKey("InstanceId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("TNO.Entities.User", "User")
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Instance");

                    b.Navigation("User");
                });

            modelBuilder.Entity("TNO.Entities.UserSource", b =>
                {
                    b.HasOne("TNO.Entities.Source", "Source")
                        .WithMany("UsersManyToMany")
                        .HasForeignKey("SourceId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("TNO.Entities.User", "User")
                        .WithMany("SourcesManyToMany")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Source");

                    b.Navigation("User");
                });

            modelBuilder.Entity("TNO.Entities.UserUpdateHistory", b =>
                {
                    b.HasOne("TNO.Entities.User", "User")
                        .WithMany("UserUpdateHistory")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("User");
                });

            modelBuilder.Entity("TNO.Entities.WorkOrder", b =>
                {
                    b.HasOne("TNO.Entities.User", "Assigned")
                        .WithMany("WorkOrdersAssigned")
                        .HasForeignKey("AssignedId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("TNO.Entities.Content", "Content")
                        .WithMany("WorkOrders")
                        .HasForeignKey("ContentId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("TNO.Entities.User", "Requestor")
                        .WithMany("WorkOrderRequests")
                        .HasForeignKey("RequestorId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.Navigation("Assigned");

                    b.Navigation("Content");

                    b.Navigation("Requestor");
                });

            modelBuilder.Entity("TNO.Entities.AVOverviewInstance", b =>
                {
                    b.Navigation("Sections");

                    b.Navigation("UserInstances");
                });

            modelBuilder.Entity("TNO.Entities.AVOverviewSection", b =>
                {
                    b.Navigation("Items");
                });

            modelBuilder.Entity("TNO.Entities.AVOverviewTemplate", b =>
                {
                    b.Navigation("Instances");

                    b.Navigation("Sections");

                    b.Navigation("SubscribersManyToMany");
                });

            modelBuilder.Entity("TNO.Entities.AVOverviewTemplateSection", b =>
                {
                    b.Navigation("Items");
                });

            modelBuilder.Entity("TNO.Entities.Action", b =>
                {
                    b.Navigation("ContentTypes");

                    b.Navigation("ContentsManyToMany");
                });

            modelBuilder.Entity("TNO.Entities.ChartTemplate", b =>
                {
                    b.Navigation("ReportSectionsManyToMany");

                    b.Navigation("ReportTemplatesManyToMany");
                });

            modelBuilder.Entity("TNO.Entities.Connection", b =>
                {
                    b.Navigation("DataLocations");

                    b.Navigation("DestinationIngests");

                    b.Navigation("SourceIngests");
                });

            modelBuilder.Entity("TNO.Entities.Content", b =>
                {
                    b.Navigation("ActionsManyToMany");

                    b.Navigation("FileReferences");

                    b.Navigation("FoldersManyToMany");

                    b.Navigation("Labels");

                    b.Navigation("Links");

                    b.Navigation("Logs");

                    b.Navigation("NotificationsManyToMany");

                    b.Navigation("Quotes");

                    b.Navigation("ReportsManyToMany");

                    b.Navigation("TagsManyToMany");

                    b.Navigation("TimeTrackings");

                    b.Navigation("TonePoolsManyToMany");

                    b.Navigation("TopicsManyToMany");

                    b.Navigation("UserNotifications");

                    b.Navigation("WorkOrders");
                });

            modelBuilder.Entity("TNO.Entities.Contributor", b =>
                {
                    b.Navigation("Contents");
                });

            modelBuilder.Entity("TNO.Entities.DataLocation", b =>
                {
                    b.Navigation("IngestsManyToMany");
                });

            modelBuilder.Entity("TNO.Entities.Filter", b =>
                {
                    b.Navigation("Folders");

                    b.Navigation("ReportSections");
                });

            modelBuilder.Entity("TNO.Entities.Folder", b =>
                {
                    b.Navigation("ContentManyToMany");

                    b.Navigation("Events");

                    b.Navigation("ReportSections");
                });

            modelBuilder.Entity("TNO.Entities.Ingest", b =>
                {
                    b.Navigation("DataLocationsManyToMany");

                    b.Navigation("SchedulesManyToMany");

                    b.Navigation("State");
                });

            modelBuilder.Entity("TNO.Entities.IngestType", b =>
                {
                    b.Navigation("Contents");

                    b.Navigation("Ingests");
                });

            modelBuilder.Entity("TNO.Entities.License", b =>
                {
                    b.Navigation("Contents");

                    b.Navigation("Sources");
                });

            modelBuilder.Entity("TNO.Entities.MediaType", b =>
                {
                    b.Navigation("Contents");

                    b.Navigation("Ingests");

                    b.Navigation("SeriesSearchMappingsManyToMany");

                    b.Navigation("SourceSearchMappingsManyToMany");

                    b.Navigation("Sources");

                    b.Navigation("UsersManyToMany");
                });

            modelBuilder.Entity("TNO.Entities.Metric", b =>
                {
                    b.Navigation("SourcesManyToMany");
                });

            modelBuilder.Entity("TNO.Entities.Notification", b =>
                {
                    b.Navigation("Instances");

                    b.Navigation("Schedules");

                    b.Navigation("SubscribersManyToMany");
                });

            modelBuilder.Entity("TNO.Entities.NotificationTemplate", b =>
                {
                    b.Navigation("Notifications");
                });

            modelBuilder.Entity("TNO.Entities.Organization", b =>
                {
                    b.Navigation("Children");

                    b.Navigation("Ministers");

                    b.Navigation("UsersManyToMany");
                });

            modelBuilder.Entity("TNO.Entities.Product", b =>
                {
                    b.Navigation("SubscribersManyToMany");
                });

            modelBuilder.Entity("TNO.Entities.Report", b =>
                {
                    b.Navigation("Events");

                    b.Navigation("Instances");

                    b.Navigation("Sections");

                    b.Navigation("SubscribersManyToMany");
                });

            modelBuilder.Entity("TNO.Entities.ReportInstance", b =>
                {
                    b.Navigation("ContentManyToMany");

                    b.Navigation("UserInstances");
                });

            modelBuilder.Entity("TNO.Entities.ReportSection", b =>
                {
                    b.Navigation("ChartTemplatesManyToMany");
                });

            modelBuilder.Entity("TNO.Entities.ReportTemplate", b =>
                {
                    b.Navigation("ChartTemplatesManyToMany");

                    b.Navigation("Reports");
                });

            modelBuilder.Entity("TNO.Entities.Schedule", b =>
                {
                    b.Navigation("Events");

                    b.Navigation("IngestsManyToMany");
                });

            modelBuilder.Entity("TNO.Entities.Series", b =>
                {
                    b.Navigation("Contents");

                    b.Navigation("MediaTypeSearchMappingsManyToMany");

                    b.Navigation("ScoreRules");
                });

            modelBuilder.Entity("TNO.Entities.Source", b =>
                {
                    b.Navigation("Contents");

                    b.Navigation("Contributors");

                    b.Navigation("EarnedMedia");

                    b.Navigation("Ingests");

                    b.Navigation("MediaTypeSearchMappingsManyToMany");

                    b.Navigation("MetricsManyToMany");

                    b.Navigation("ScoreRules");

                    b.Navigation("Series");

                    b.Navigation("UsersManyToMany");
                });

            modelBuilder.Entity("TNO.Entities.Tag", b =>
                {
                    b.Navigation("ContentsManyToMany");
                });

            modelBuilder.Entity("TNO.Entities.TonePool", b =>
                {
                    b.Navigation("ContentsManyToMany");
                });

            modelBuilder.Entity("TNO.Entities.Topic", b =>
                {
                    b.Navigation("ContentsManyToMany");
                });

            modelBuilder.Entity("TNO.Entities.User", b =>
                {
                    b.Navigation("AVOverviewSubscriptionsManyToMany");

                    b.Navigation("ColleaguesManyToMany");

                    b.Navigation("ContentNotifications");

                    b.Navigation("Contents");

                    b.Navigation("Distribution");

                    b.Navigation("Filters");

                    b.Navigation("Folders");

                    b.Navigation("MediaTypesManyToMany");

                    b.Navigation("NotificationSubscriptionsManyToMany");

                    b.Navigation("Notifications");

                    b.Navigation("OrganizationsManyToMany");

                    b.Navigation("ProductSubscriptionsManyToMany");

                    b.Navigation("ReportInstances");

                    b.Navigation("ReportSubscriptionsManyToMany");

                    b.Navigation("Reports");

                    b.Navigation("SourcesManyToMany");

                    b.Navigation("TimeTrackings");

                    b.Navigation("TonePools");

                    b.Navigation("UserUpdateHistory");

                    b.Navigation("WorkOrderRequests");

                    b.Navigation("WorkOrdersAssigned");
                });
#pragma warning restore 612, 618
        }
    }
}
