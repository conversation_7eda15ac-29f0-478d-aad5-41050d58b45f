{"format": 1, "restore": {"C:\\Users\\<USER>\\Documents\\GitHub\\tno\\libs\\net\\reports\\TNO.Reports.csproj": {}}, "projects": {"C:\\Users\\<USER>\\Documents\\GitHub\\tno\\libs\\net\\ches\\TNO.Ches.csproj": {"version": "2.0.1", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Documents\\GitHub\\tno\\libs\\net\\ches\\TNO.Ches.csproj", "projectName": "TNO.Ches", "projectPath": "C:\\Users\\<USER>\\Documents\\GitHub\\tno\\libs\\net\\ches\\TNO.Ches.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Documents\\GitHub\\tno\\libs\\net\\ches\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\Documents\\GitHub\\tno\\NuGet.Config", "C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Users\\<USER>\\Documents\\GitHub\\tno\\libs\\net\\packages": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"C:\\Users\\<USER>\\Documents\\GitHub\\tno\\libs\\net\\core\\TNO.Core.csproj": {"projectPath": "C:\\Users\\<USER>\\Documents\\GitHub\\tno\\libs\\net\\core\\TNO.Core.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"Microsoft.AspNetCore": {"target": "Package", "version": "[2.2.0, )"}, "Microsoft.AspNetCore.Http": {"target": "Package", "version": "[2.2.2, )"}, "Microsoft.AspNetCore.Server.IIS": {"target": "Package", "version": "[2.2.6, )"}, "Microsoft.Extensions.Http": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.Extensions.Options": {"target": "Package", "version": "[9.0.0, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}, "System.IdentityModel.Tokens.Jwt": {"target": "Package", "version": "[8.2.1, )"}, "System.Net.Http": {"target": "Package", "version": "[4.3.4, )"}, "System.Private.Uri": {"target": "Package", "version": "[4.3.2, )"}, "System.Text.Json": {"target": "Package", "version": "[9.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Runtime.linux-arm", "version": "[9.0.7, 9.0.7]"}, {"name": "Microsoft.AspNetCore.App.Runtime.linux-arm64", "version": "[9.0.7, 9.0.7]"}, {"name": "Microsoft.AspNetCore.App.Runtime.linux-x64", "version": "[9.0.7, 9.0.7]"}, {"name": "Microsoft.AspNetCore.App.Runtime.win-x64", "version": "[9.0.7, 9.0.7]"}, {"name": "Microsoft.NETCore.App.Host.linux-arm", "version": "[9.0.7, 9.0.7]"}, {"name": "Microsoft.NETCore.App.Host.linux-arm64", "version": "[9.0.7, 9.0.7]"}, {"name": "Microsoft.NETCore.App.Host.linux-x64", "version": "[9.0.7, 9.0.7]"}, {"name": "Microsoft.NETCore.App.Runtime.linux-arm", "version": "[9.0.7, 9.0.7]"}, {"name": "Microsoft.NETCore.App.Runtime.linux-arm64", "version": "[9.0.7, 9.0.7]"}, {"name": "Microsoft.NETCore.App.Runtime.linux-x64", "version": "[9.0.7, 9.0.7]"}, {"name": "Microsoft.NETCore.App.Runtime.win-x64", "version": "[9.0.7, 9.0.7]"}, {"name": "Microsoft.WindowsDesktop.App.Runtime.win-x64", "version": "[9.0.7, 9.0.7]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.302/PortableRuntimeIdentifierGraph.json"}}, "runtimes": {"linux-arm": {"#import": []}, "linux-arm64": {"#import": []}, "linux-x64": {"#import": []}, "win-x64": {"#import": []}}}, "C:\\Users\\<USER>\\Documents\\GitHub\\tno\\libs\\net\\core\\TNO.Core.csproj": {"version": "2.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Documents\\GitHub\\tno\\libs\\net\\core\\TNO.Core.csproj", "projectName": "TNO.Core", "projectPath": "C:\\Users\\<USER>\\Documents\\GitHub\\tno\\libs\\net\\core\\TNO.Core.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Documents\\GitHub\\tno\\libs\\net\\core\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\Documents\\GitHub\\tno\\NuGet.Config", "C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Users\\<USER>\\Documents\\GitHub\\tno\\libs\\net\\packages": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"AWSSDK.Extensions.NETCore.Setup": {"target": "Package", "version": "[3.7.301, )"}, "AWSSDK.S3": {"target": "Package", "version": "[3.7.402.5, )"}, "Microsoft.AspNetCore.Http": {"target": "Package", "version": "[2.2.2, )"}, "Microsoft.EntityFrameworkCore": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.EntityFrameworkCore.Design": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[9.0.0, )"}, "Microsoft.EntityFrameworkCore.Tools": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[9.0.0, )"}, "Microsoft.Extensions.Configuration.Abstractions": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.Extensions.Configuration.Binder": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.Extensions.Http": {"target": "Package", "version": "[9.0.0, )"}, "System.IdentityModel.Tokens.Jwt": {"target": "Package", "version": "[8.2.1, )"}, "System.Text.Encodings.Web": {"target": "Package", "version": "[9.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Runtime.linux-arm", "version": "[9.0.7, 9.0.7]"}, {"name": "Microsoft.AspNetCore.App.Runtime.linux-arm64", "version": "[9.0.7, 9.0.7]"}, {"name": "Microsoft.AspNetCore.App.Runtime.linux-x64", "version": "[9.0.7, 9.0.7]"}, {"name": "Microsoft.AspNetCore.App.Runtime.win-x64", "version": "[9.0.7, 9.0.7]"}, {"name": "Microsoft.NETCore.App.Host.linux-arm", "version": "[9.0.7, 9.0.7]"}, {"name": "Microsoft.NETCore.App.Host.linux-arm64", "version": "[9.0.7, 9.0.7]"}, {"name": "Microsoft.NETCore.App.Host.linux-x64", "version": "[9.0.7, 9.0.7]"}, {"name": "Microsoft.NETCore.App.Runtime.linux-arm", "version": "[9.0.7, 9.0.7]"}, {"name": "Microsoft.NETCore.App.Runtime.linux-arm64", "version": "[9.0.7, 9.0.7]"}, {"name": "Microsoft.NETCore.App.Runtime.linux-x64", "version": "[9.0.7, 9.0.7]"}, {"name": "Microsoft.NETCore.App.Runtime.win-x64", "version": "[9.0.7, 9.0.7]"}, {"name": "Microsoft.WindowsDesktop.App.Runtime.win-x64", "version": "[9.0.7, 9.0.7]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.302/PortableRuntimeIdentifierGraph.json"}}, "runtimes": {"linux-arm": {"#import": []}, "linux-arm64": {"#import": []}, "linux-x64": {"#import": []}, "win-x64": {"#import": []}}}, "C:\\Users\\<USER>\\Documents\\GitHub\\tno\\libs\\net\\dal\\TNO.DAL.csproj": {"version": "2.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Documents\\GitHub\\tno\\libs\\net\\dal\\TNO.DAL.csproj", "projectName": "TNO.DAL", "projectPath": "C:\\Users\\<USER>\\Documents\\GitHub\\tno\\libs\\net\\dal\\TNO.DAL.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Documents\\GitHub\\tno\\libs\\net\\dal\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\Documents\\GitHub\\tno\\NuGet.Config", "C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Users\\<USER>\\Documents\\GitHub\\tno\\libs\\net\\packages": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"C:\\Users\\<USER>\\Documents\\GitHub\\tno\\libs\\net\\ches\\TNO.Ches.csproj": {"projectPath": "C:\\Users\\<USER>\\Documents\\GitHub\\tno\\libs\\net\\ches\\TNO.Ches.csproj"}, "C:\\Users\\<USER>\\Documents\\GitHub\\tno\\libs\\net\\core\\TNO.Core.csproj": {"projectPath": "C:\\Users\\<USER>\\Documents\\GitHub\\tno\\libs\\net\\core\\TNO.Core.csproj"}, "C:\\Users\\<USER>\\Documents\\GitHub\\tno\\libs\\net\\elastic\\TNO.Elastic.csproj": {"projectPath": "C:\\Users\\<USER>\\Documents\\GitHub\\tno\\libs\\net\\elastic\\TNO.Elastic.csproj"}, "C:\\Users\\<USER>\\Documents\\GitHub\\tno\\libs\\net\\entities\\TNO.Entities.csproj": {"projectPath": "C:\\Users\\<USER>\\Documents\\GitHub\\tno\\libs\\net\\entities\\TNO.Entities.csproj"}, "C:\\Users\\<USER>\\Documents\\GitHub\\tno\\libs\\net\\models\\TNO.Models.csproj": {"projectPath": "C:\\Users\\<USER>\\Documents\\GitHub\\tno\\libs\\net\\models\\TNO.Models.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"DotNetEnv": {"target": "Package", "version": "[3.1.1, )"}, "LinqKit": {"target": "Package", "version": "[1.3.7, )"}, "Microsoft.AspNetCore.Authentication.JwtBearer": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.AspNetCore.Identity.EntityFrameworkCore": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.EntityFrameworkCore": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.EntityFrameworkCore.Design": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[9.0.0, )"}, "Microsoft.EntityFrameworkCore.Relational": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.EntityFrameworkCore.Tools": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[9.0.0, )"}, "NPOI": {"target": "Package", "version": "[2.7.2, )"}, "Npgsql.EntityFrameworkCore.PostgreSQL": {"target": "Package", "version": "[9.0.1, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.302/PortableRuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Documents\\GitHub\\tno\\libs\\net\\elastic\\TNO.Elastic.csproj": {"version": "2.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Documents\\GitHub\\tno\\libs\\net\\elastic\\TNO.Elastic.csproj", "projectName": "TNO.Elastic", "projectPath": "C:\\Users\\<USER>\\Documents\\GitHub\\tno\\libs\\net\\elastic\\TNO.Elastic.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Documents\\GitHub\\tno\\libs\\net\\elastic\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\Documents\\GitHub\\tno\\NuGet.Config", "C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Users\\<USER>\\Documents\\GitHub\\tno\\libs\\net\\packages": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"C:\\Users\\<USER>\\Documents\\GitHub\\tno\\libs\\net\\core\\TNO.Core.csproj": {"projectPath": "C:\\Users\\<USER>\\Documents\\GitHub\\tno\\libs\\net\\core\\TNO.Core.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"Elasticsearch.Net": {"target": "Package", "version": "[7.17.5, )"}, "Microsoft.Extensions.Hosting": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.Extensions.Options.DataAnnotations": {"target": "Package", "version": "[9.0.0, )"}, "NEST": {"target": "Package", "version": "[7.17.5, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Runtime.linux-arm", "version": "[9.0.7, 9.0.7]"}, {"name": "Microsoft.AspNetCore.App.Runtime.linux-arm64", "version": "[9.0.7, 9.0.7]"}, {"name": "Microsoft.AspNetCore.App.Runtime.linux-x64", "version": "[9.0.7, 9.0.7]"}, {"name": "Microsoft.AspNetCore.App.Runtime.win-x64", "version": "[9.0.7, 9.0.7]"}, {"name": "Microsoft.NETCore.App.Host.linux-arm", "version": "[9.0.7, 9.0.7]"}, {"name": "Microsoft.NETCore.App.Host.linux-arm64", "version": "[9.0.7, 9.0.7]"}, {"name": "Microsoft.NETCore.App.Host.linux-x64", "version": "[9.0.7, 9.0.7]"}, {"name": "Microsoft.NETCore.App.Runtime.linux-arm", "version": "[9.0.7, 9.0.7]"}, {"name": "Microsoft.NETCore.App.Runtime.linux-arm64", "version": "[9.0.7, 9.0.7]"}, {"name": "Microsoft.NETCore.App.Runtime.linux-x64", "version": "[9.0.7, 9.0.7]"}, {"name": "Microsoft.NETCore.App.Runtime.win-x64", "version": "[9.0.7, 9.0.7]"}, {"name": "Microsoft.WindowsDesktop.App.Runtime.win-x64", "version": "[9.0.7, 9.0.7]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.302/PortableRuntimeIdentifierGraph.json"}}, "runtimes": {"linux-arm": {"#import": []}, "linux-arm64": {"#import": []}, "linux-x64": {"#import": []}, "win-x64": {"#import": []}}}, "C:\\Users\\<USER>\\Documents\\GitHub\\tno\\libs\\net\\entities\\TNO.Entities.csproj": {"version": "2.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Documents\\GitHub\\tno\\libs\\net\\entities\\TNO.Entities.csproj", "projectName": "TNO.Entities", "projectPath": "C:\\Users\\<USER>\\Documents\\GitHub\\tno\\libs\\net\\entities\\TNO.Entities.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Documents\\GitHub\\tno\\libs\\net\\entities\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\Documents\\GitHub\\tno\\NuGet.Config", "C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Users\\<USER>\\Documents\\GitHub\\tno\\libs\\net\\packages": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"C:\\Users\\<USER>\\Documents\\GitHub\\tno\\libs\\net\\core\\TNO.Core.csproj": {"projectPath": "C:\\Users\\<USER>\\Documents\\GitHub\\tno\\libs\\net\\core\\TNO.Core.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"Microsoft.EntityFrameworkCore": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.EntityFrameworkCore.Design": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[9.0.0, )"}, "Microsoft.EntityFrameworkCore.Relational": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.EntityFrameworkCore.Tools": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[9.0.0, )"}, "Npgsql.EntityFrameworkCore.PostgreSQL": {"target": "Package", "version": "[9.0.1, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Runtime.linux-arm", "version": "[9.0.7, 9.0.7]"}, {"name": "Microsoft.AspNetCore.App.Runtime.linux-arm64", "version": "[9.0.7, 9.0.7]"}, {"name": "Microsoft.AspNetCore.App.Runtime.linux-x64", "version": "[9.0.7, 9.0.7]"}, {"name": "Microsoft.AspNetCore.App.Runtime.win-x64", "version": "[9.0.7, 9.0.7]"}, {"name": "Microsoft.NETCore.App.Host.linux-arm", "version": "[9.0.7, 9.0.7]"}, {"name": "Microsoft.NETCore.App.Host.linux-arm64", "version": "[9.0.7, 9.0.7]"}, {"name": "Microsoft.NETCore.App.Host.linux-x64", "version": "[9.0.7, 9.0.7]"}, {"name": "Microsoft.NETCore.App.Runtime.linux-arm", "version": "[9.0.7, 9.0.7]"}, {"name": "Microsoft.NETCore.App.Runtime.linux-arm64", "version": "[9.0.7, 9.0.7]"}, {"name": "Microsoft.NETCore.App.Runtime.linux-x64", "version": "[9.0.7, 9.0.7]"}, {"name": "Microsoft.NETCore.App.Runtime.win-x64", "version": "[9.0.7, 9.0.7]"}, {"name": "Microsoft.WindowsDesktop.App.Runtime.win-x64", "version": "[9.0.7, 9.0.7]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.302/PortableRuntimeIdentifierGraph.json"}}, "runtimes": {"linux-arm": {"#import": []}, "linux-arm64": {"#import": []}, "linux-x64": {"#import": []}, "win-x64": {"#import": []}}}, "C:\\Users\\<USER>\\Documents\\GitHub\\tno\\libs\\net\\models\\TNO.Models.csproj": {"version": "2.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Documents\\GitHub\\tno\\libs\\net\\models\\TNO.Models.csproj", "projectName": "TNO.Models", "projectPath": "C:\\Users\\<USER>\\Documents\\GitHub\\tno\\libs\\net\\models\\TNO.Models.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Documents\\GitHub\\tno\\libs\\net\\models\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\Documents\\GitHub\\tno\\NuGet.Config", "C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Users\\<USER>\\Documents\\GitHub\\tno\\libs\\net\\packages": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"C:\\Users\\<USER>\\Documents\\GitHub\\tno\\libs\\net\\entities\\TNO.Entities.csproj": {"projectPath": "C:\\Users\\<USER>\\Documents\\GitHub\\tno\\libs\\net\\entities\\TNO.Entities.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"FTTLib.dll": {"target": "Package", "version": "[1.1.7, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Runtime.linux-arm", "version": "[9.0.7, 9.0.7]"}, {"name": "Microsoft.AspNetCore.App.Runtime.linux-arm64", "version": "[9.0.7, 9.0.7]"}, {"name": "Microsoft.AspNetCore.App.Runtime.linux-x64", "version": "[9.0.7, 9.0.7]"}, {"name": "Microsoft.AspNetCore.App.Runtime.win-x64", "version": "[9.0.7, 9.0.7]"}, {"name": "Microsoft.NETCore.App.Host.linux-arm", "version": "[9.0.7, 9.0.7]"}, {"name": "Microsoft.NETCore.App.Host.linux-arm64", "version": "[9.0.7, 9.0.7]"}, {"name": "Microsoft.NETCore.App.Host.linux-x64", "version": "[9.0.7, 9.0.7]"}, {"name": "Microsoft.NETCore.App.Runtime.linux-arm", "version": "[9.0.7, 9.0.7]"}, {"name": "Microsoft.NETCore.App.Runtime.linux-arm64", "version": "[9.0.7, 9.0.7]"}, {"name": "Microsoft.NETCore.App.Runtime.linux-x64", "version": "[9.0.7, 9.0.7]"}, {"name": "Microsoft.NETCore.App.Runtime.win-x64", "version": "[9.0.7, 9.0.7]"}, {"name": "Microsoft.WindowsDesktop.App.Runtime.win-x64", "version": "[9.0.7, 9.0.7]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.302/PortableRuntimeIdentifierGraph.json"}}, "runtimes": {"linux-arm": {"#import": []}, "linux-arm64": {"#import": []}, "linux-x64": {"#import": []}, "win-x64": {"#import": []}}}, "C:\\Users\\<USER>\\Documents\\GitHub\\tno\\libs\\net\\reports\\TNO.Reports.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Documents\\GitHub\\tno\\libs\\net\\reports\\TNO.Reports.csproj", "projectName": "TNO.Reports", "projectPath": "C:\\Users\\<USER>\\Documents\\GitHub\\tno\\libs\\net\\reports\\TNO.Reports.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Documents\\GitHub\\tno\\libs\\net\\reports\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\Documents\\GitHub\\tno\\NuGet.Config", "C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Users\\<USER>\\Documents\\GitHub\\tno\\libs\\net\\packages": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"C:\\Users\\<USER>\\Documents\\GitHub\\tno\\libs\\net\\dal\\TNO.DAL.csproj": {"projectPath": "C:\\Users\\<USER>\\Documents\\GitHub\\tno\\libs\\net\\dal\\TNO.DAL.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"NPOI": {"target": "Package", "version": "[2.7.2, )"}, "SixLabors.ImageSharp": {"target": "Package", "version": "[3.1.7, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.302/PortableRuntimeIdentifierGraph.json"}}}}}