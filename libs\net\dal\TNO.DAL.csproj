﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <OutputType>Library</OutputType>
    <RootNamespace>TNO.DAL</RootNamespace>
    <Version>2.0.0.0</Version>
    <AssemblyVersion>2.0.0.0</AssemblyVersion>
    <PackageId>TNO.DAL</PackageId>
    <Authors><PERSON></Authors>
    <Company>Fosol Solutions Inc.</Company>
    <PackageOutputPath>../packages</PackageOutputPath>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\core\TNO.Core.csproj" />
    <ProjectReference Include="..\elastic\TNO.Elastic.csproj" />
    <ProjectReference Include="..\entities\TNO.Entities.csproj" />
    <ProjectReference Include="..\models\TNO.Models.csproj" />
    <ProjectReference Include="..\ches\TNO.Ches.csproj" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="DotNetEnv" Version="3.1.1" />
    <PackageReference Include="LinqKit" Version="1.3.7" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="9.0.0" />
    <PackageReference Include="Microsoft.AspNetCore.Identity.EntityFrameworkCore" Version="9.0.0" />
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="9.0.0" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="9.0.0">
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      <PrivateAssets>all</PrivateAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.EntityFrameworkCore.Relational" Version="9.0.0" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="9.0.0">
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      <PrivateAssets>all</PrivateAssets>
    </PackageReference>
    <PackageReference Include="Npgsql.EntityFrameworkCore.PostgreSQL" Version="9.0.1" />
    <PackageReference Include="NPOI" Version="2.7.2" />
  </ItemGroup>

  <ItemGroup>
    <Content Include="Migrations\**\*.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
  </ItemGroup>

</Project>
