{"version": 2, "dgSpecHash": "rd8y1ezwUsQ=", "success": false, "projectFilePath": "C:\\Users\\<USER>\\Documents\\GitHub\\tno\\services\\net\\nlp\\TNO.Services.NLP.csproj", "expectedPackageFiles": [], "logs": [{"code": "NU1301", "level": "Error", "message": "The local source 'C:\\root\\.nuget\\packages' doesn't exist.", "projectPath": "C:\\Users\\<USER>\\Documents\\GitHub\\tno\\services\\net\\nlp\\TNO.Services.NLP.csproj", "filePath": "C:\\Users\\<USER>\\Documents\\GitHub\\tno\\services\\net\\nlp\\TNO.Services.NLP.csproj", "libraryId": "Microsoft.ML", "targetGraphs": []}, {"code": "NU1301", "level": "Error", "message": "The local source 'C:\\root\\.nuget\\packages' doesn't exist.", "projectPath": "C:\\Users\\<USER>\\Documents\\GitHub\\tno\\services\\net\\nlp\\TNO.Services.NLP.csproj", "filePath": "C:\\Users\\<USER>\\Documents\\GitHub\\tno\\services\\net\\nlp\\TNO.Services.NLP.csproj", "libraryId": "System.Formats.Asn1", "targetGraphs": []}, {"code": "NU1301", "level": "Error", "message": "The local source 'C:\\root\\.nuget\\packages' doesn't exist.", "projectPath": "C:\\Users\\<USER>\\Documents\\GitHub\\tno\\services\\net\\nlp\\TNO.Services.NLP.csproj", "filePath": "C:\\Users\\<USER>\\Documents\\GitHub\\tno\\services\\net\\nlp\\TNO.Services.NLP.csproj", "libraryId": "TNO.Services", "targetGraphs": []}]}