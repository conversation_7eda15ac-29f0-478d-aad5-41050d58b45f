---
# How the app will be deployed to the pod.
kind: DeploymentConfig
apiVersion: apps.openshift.io/v1
metadata:
  name: reporting-service
  namespace: default
  annotations:
    description: Defines how to deploy reporting-service
    created-by: jeremy.foster
  labels:
    name: reporting-service
    part-of: tno
    version: 1.0.0
    component: reporting-service
    managed-by: kustomize
spec:
  replicas: 1
  selector:
    name: reporting-service
    part-of: tno
    component: reporting-service
  strategy:
    rollingParams:
      intervalSeconds: 1
      maxSurge: 25%
      maxUnavailable: 25%
      timeoutSeconds: 600
      updatePeriodSeconds: 1
    type: Rolling
  template:
    metadata:
      name: reporting-service
      labels:
        name: reporting-service
        part-of: tno
        component: reporting-service
    spec:
      containers:
        - name: reporting-service
          image: ""
          imagePullPolicy: Always
          ports:
            - containerPort: 8080
              protocol: TCP
          resources:
            requests:
              cpu: 20m
              memory: 80Mi
          env:
            # .NET Configuration
            - name: ASPNETCORE_ENVIRONMENT
              value: Production
            - name: ASPNETCORE_URLS
              value: http://+:8080

            - name: Logging__LogLevel__TNO
              value: Information

            # Common Service Configuration
            - name: Service__ApiUrl
              valueFrom:
                configMapKeyRef:
                  name: services
                  key: API_HOST_URL
            - name: Service__EmailTo
              valueFrom:
                configMapKeyRef:
                  name: services
                  key: EMAIL_FAILURE_TO

            # Authentication Configuration
            - name: Auth__Keycloak__Authority
              valueFrom:
                configMapKeyRef:
                  name: services
                  key: KEYCLOAK_AUTHORITY
            - name: Auth__Keycloak__Audience
              valueFrom:
                configMapKeyRef:
                  name: services
                  key: KEYCLOAK_AUDIENCE
            - name: Auth__Keycloak__Secret
              valueFrom:
                secretKeyRef:
                  name: keycloak
                  key: KEYCLOAK_CLIENT_SECRET

            - name: Kafka__Admin__ClientId
              valueFrom:
                configMapKeyRef:
                  name: reporting-service
                  key: KAFKA_CLIENT_ID
            - name: Kafka__Admin__BootstrapServers
              valueFrom:
                configMapKeyRef:
                  name: services
                  key: KAFKA_BOOTSTRAP_SERVERS

            - name: Kafka__Consumer__GroupId
              valueFrom:
                configMapKeyRef:
                  name: reporting-service
                  key: KAFKA_CLIENT_ID
            - name: Kafka__Consumer__BootstrapServers
              valueFrom:
                configMapKeyRef:
                  name: services
                  key: KAFKA_BOOTSTRAP_SERVERS

            - name: Kafka__Producer__ClientId
              valueFrom:
                configMapKeyRef:
                  name: reporting-service
                  key: KAFKA_CLIENT_ID
            - name: Kafka__Producer__BootstrapServers
              valueFrom:
                configMapKeyRef:
                  name: services
                  key: KAFKA_BOOTSTRAP_SERVERS

            # Shared Reporting Configuration
            - name: Reporting__SubscriberAppUrl
              valueFrom:
                configMapKeyRef:
                  name: reporting-shared
                  key: REPORTING_SUBSCRIBER_URL
            - name: Reporting__ViewContentUrl
              valueFrom:
                configMapKeyRef:
                  name: reporting-shared
                  key: REPORTING_VIEW_CONTENT_URL
            - name: Reporting__RequestTranscriptUrl
              valueFrom:
                configMapKeyRef:
                  name: reporting-shared
                  key: REPORTING_REQUEST_TRANSCRIPT_URL
            - name: Reporting__AddToReportUrl
              valueFrom:
                configMapKeyRef:
                  name: reporting-shared
                  key: REPORTING_ADD_TO_REPORT_URL

            # Service Configuration
            - name: Service__MaxFailLimit
              valueFrom:
                configMapKeyRef:
                  name: reporting-service
                  key: MAX_FAIL_LIMIT
            - name: Service__ResendOnFailure
              valueFrom:
                configMapKeyRef:
                  name: reporting-service
                  key: RESEND_ON_FAILURE
            - name: Service__SendToAllSubscribersBeforeFailing
              valueFrom:
                configMapKeyRef:
                  name: reporting-service
                  key: SEND_TO_ALL_BEFORE_FAILING
            - name: Service__Topics
              valueFrom:
                configMapKeyRef:
                  name: reporting-service
                  key: TOPICS
            - name: Service__DefaultFrom
              valueFrom:
                configMapKeyRef:
                  name: ches
                  key: CHES_FROM

            # CHES Configuration
            - name: CHES__EmailEnabled
              valueFrom:
                configMapKeyRef:
                  name: reporting-service
                  key: CHES_EMAIL_ENABLED
            - name: CHES__EmailAuthorized
              valueFrom:
                configMapKeyRef:
                  name: reporting-service
                  key: CHES_EMAIL_AUTHORIZED
            - name: CHES__AlwaysBcc
              valueFrom:
                configMapKeyRef:
                  name: reporting-service
                  key: ALWAYS_BCC

            - name: CHES__AuthUrl
              valueFrom:
                configMapKeyRef:
                  name: ches
                  key: CHES_AUTH_URL
            - name: CHES__HostUri
              valueFrom:
                configMapKeyRef:
                  name: ches
                  key: CHES_HOST_URI
            - name: CHES__Username
              valueFrom:
                secretKeyRef:
                  name: ches
                  key: USERNAME
            - name: CHES__Password
              valueFrom:
                secretKeyRef:
                  name: ches
                  key: PASSWORD

            - name: Charts__Url
              valueFrom:
                configMapKeyRef:
                  name: reporting-service
                  key: CHARTS_URL

          livenessProbe:
            httpGet:
              path: "/health"
              port: 8080
              scheme: HTTP
            initialDelaySeconds: 30
            timeoutSeconds: 30
            periodSeconds: 20
            successThreshold: 1
            failureThreshold: 3
          readinessProbe:
            httpGet:
              path: "/health"
              port: 8080
              scheme: HTTP
            initialDelaySeconds: 30
            timeoutSeconds: 30
            periodSeconds: 20
            successThreshold: 1
            failureThreshold: 3
      dnsPolicy: ClusterFirst
      restartPolicy: Always
      securityContext: {}
      terminationGracePeriodSeconds: 30
  test: false
  triggers:
    - type: ConfigChange
    - type: ImageChange
      imageChangeParams:
        automatic: true
        containerNames:
          - reporting-service
        from:
          kind: ImageStreamTag
          namespace: 9b301c-tools
          name: reporting-service:dev
